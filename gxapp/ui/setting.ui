<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>502</width>
    <height>468</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>配置</string>
  </property>
  <widget class="Line" name="line">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>210</y>
     <width>501</width>
     <height>16</height>
    </rect>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>250</y>
     <width>67</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>线损：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditLineLoss">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>250</y>
     <width>113</width>
     <height>31</height>
    </rect>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>250</y>
     <width>67</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>dB</string>
   </property>
  </widget>
  <widget class="Line" name="line_2">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>300</y>
     <width>501</width>
     <height>16</height>
    </rect>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>320</y>
     <width>81</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>通用配置：</string>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButtonTest">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>20</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>检测按钮</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>20</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>仪器在线检测：</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>410</y>
     <width>111</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>报告输出路径：</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEditSaveReportPath">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>410</y>
     <width>271</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QPushButton" name="pushButtonSaveReportPath">
   <property name="geometry">
    <rect>
     <x>400</x>
     <y>410</y>
     <width>70</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>...</string>
   </property>
  </widget>
  <widget class="GxSerialComboBox" name="comboBoxSerialPort">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>360</y>
     <width>241</width>
     <height>31</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="labelSerialPort">
   <property name="geometry">
    <rect>
     <x>76</x>
     <y>360</y>
     <width>51</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>串口：</string>
   </property>
  </widget>
  <widget class="QLabel" name="labelDcPower">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>80</y>
     <width>211</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>直流电源</string>
   </property>
  </widget>
  <widget class="QLabel" name="labelSpectrumAnalyzer">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>80</y>
     <width>211</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>频谱仪</string>
   </property>
  </widget>
  <widget class="QLabel" name="labelSignalGenerator">
   <property name="geometry">
    <rect>
     <x>40</x>
     <y>130</y>
     <width>211</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>矢量信号发生器</string>
   </property>
  </widget>
  <widget class="QLabel" name="labelBluetoothTester">
   <property name="geometry">
    <rect>
     <x>270</x>
     <y>130</y>
     <width>211</width>
     <height>31</height>
    </rect>
   </property>
   <property name="text">
    <string>蓝牙综测仪</string>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>GxSerialComboBox</class>
   <extends>QComboBox</extends>
   <header>gxapp.gxserialcombobox</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
