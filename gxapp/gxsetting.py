import serial.tools.list_ports
import os

from PyQt5 import QtWidgets, QtCore, uic
from PyQt5.QtWidgets import QFileDialog
from PyQt5.QtCore import QThread, pyqtSignal

from gxpy.gxengine.common import resource_path
from gxapp.gxserialcombobox import GxSerialComboBox
from gxapp.device_online_check import DeviceHandShakeCheck
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData


class TestThread(QThread):
    # 创建一个信号，用于在测试完成时发送测试结果
    testCompleted = pyqtSignal(dict)

    def __init__(self, ip_addr: dict, event_engine: EventEngine, parent=None):
        super(TestThread, self).__init__(parent)
        self.ip_addr = ip_addr
        self.event_engine = event_engine

    def run(self):
        """
        test_results = {
            'dc_power': True,  # 直流电源连接
            'spectrum_analyzer': False,  # 频谱仪未连接
            'signal_generator': True,  # 矢量信号发生器连接
            'bluetooth_tester': True,  # 蓝牙综测仪连接
        }
        """
        # 这里应该是实际的测试逻辑
        online_check = DeviceHandShakeCheck(ip_addr=self.ip_addr, event_engine=self.event_engine)
        test_results = online_check.run()
        # 发送测试完成的信号，附带测试结果
        self.testCompleted.emit(test_results)


class GxSetting(QtWidgets.QDialog):
    def __init__(self, ip_addr: dict, setting_data: dict, is_test_active: bool, event_engine: EventEngine):
        super(GxSetting, self).__init__()
        self.setting_data = setting_data
        self.ip_addr = ip_addr
        self.event_engine = event_engine
        self.ui = uic.loadUi(resource_path('gxapp/ui/setting.ui'), self)
        self.setWindowTitle("配置")
        self.setWindowFlags(QtCore.Qt.WindowCloseButtonHint)                # 设置窗口标志以显示一个关闭按钮
        self.setFixedSize(self.width(), self.height())                      # 将窗口的大小固定为当前宽度和高度
        self.setModal(True)                                                 # 将窗口设置为模态窗口，这意味着在关闭模态窗口之前，用户无法与其他窗口进行交互

        self.init_serial_port_ui(is_test_active)
        self.load_settings()

        if is_test_active:
            self.disable_ui()
        else:
            self.connect_ui()

    def disable_ui(self):
        for child in self.findChildren(QtWidgets.QWidget):
            child.setEnabled(False)

    def connect_ui(self):
        self.ui.pushButtonSaveReportPath.clicked.connect(self.on_save_report_path_clicked)
        self.ui.pushButtonTest.clicked.connect(self.on_test)

    def init_serial_port_ui(self, is_test_active):
        ports = []
        for n, (portname, desc, hwid) in enumerate(sorted(serial.tools.list_ports.comports())):
            ports.append(portname)

        self.ui.comboBoxSerialPort.addItems(ports)
        if is_test_active and 'uart_port' in self.setting_data:
            self.ui.comboBoxSerialPort.setCurrentText(self.setting_data['uart_port'])
        elif 'uart_port' in self.setting_data and self.setting_data['uart_port'] in ports:
            index = ports.index(self.setting_data['uart_port'])
            self.ui.comboBoxSerialPort.setCurrentIndex(index)
        else:
            self.ui.comboBoxSerialPort.setCurrentIndex(0)

    def load_settings(self):
        self.ui.lineEditSaveReportPath.setText(self.setting_data.get('save_report_path', ''))
        self.ui.lineEditLineLoss.setText(self.setting_data.get('line_loss', ''))
        # 显示上次的检测结果，如果有的话
        self.update_device_status_labels(self.setting_data)
        # 没有检测时，检测结果显示为默认值
        if 'dc_power' not in self.setting_data:             # 初次进入配置菜单时，设备在线状态显示（此时设备状态不在self.setting_data）
            self.init_device_status_labels()
        else:
            if self.setting_data['dc_power'] is None:       # 非初次进入配置菜单时，但是上次没有进行设备在线检测（此时设备状态为None）
                self.init_device_status_labels()

    def init_device_status_labels(self):
        # 初始化标签样式
        labelStyle = "QLabel { color: grey; }"  # 默认为灰色，表示未检测状态
        self.ui.labelDcPower.setStyleSheet(labelStyle)
        self.ui.labelSpectrumAnalyzer.setStyleSheet(labelStyle)
        self.ui.labelSignalGenerator.setStyleSheet(labelStyle)
        self.ui.labelBluetoothTester.setStyleSheet(labelStyle)

        # 设置标签初始文本
        self.ui.labelDcPower.setText("直流电源: 未检测")
        self.ui.labelSpectrumAnalyzer.setText("频谱仪: 未检测")
        self.ui.labelSignalGenerator.setText("矢量信号发生器: 未检测")
        self.ui.labelBluetoothTester.setText("蓝牙综测仪: 未检测")

    def on_save_report_path_clicked(self):
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹", os.path.abspath('.'))
        self.ui.lineEditSaveReportPath.setText(folder_path)

    def on_test(self):
        self.update_device_status_labels(dict())    # 每次检测时，设备检测结果显示为检测中
        # 禁用检测按钮
        self.ui.pushButtonTest.setEnabled(False)
        print(self.ip_addr)

        # 创建并启动测试线程
        self.test_thread = TestThread(ip_addr=self.ip_addr, event_engine=self.event_engine)
        self.test_thread.testCompleted.connect(self.on_test_completed)
        self.test_thread.start()

    def on_test_completed(self, test_results):
        # 更新测试结果
        self.update_device_status_labels(test_results)
        # 测试完成后重新启用检测按钮
        self.ui.pushButtonTest.setEnabled(True)

    def update_device_status_labels(self, test_results):
        onlineStyle = "QLabel { color: green; }"
        offlineStyle = "QLabel { color: red; }"
        unknownStyle = "QLabel { color: grey; }"
    
        # 更新直流电源状态
        dc_power_status = test_results.get('dc_power', None)
        if dc_power_status is True:
            self.ui.labelDcPower.setStyleSheet(onlineStyle)
            self.ui.labelDcPower.setText("直流电源: 在线")
        elif dc_power_status is False:
            self.ui.labelDcPower.setStyleSheet(offlineStyle)
            self.ui.labelDcPower.setText("直流电源: 离线")
        else:
            self.ui.labelDcPower.setStyleSheet(unknownStyle)
            # self.ui.labelDcPower.setText("直流电源: 未检测")
            self.ui.labelDcPower.setText("直流电源: 检测中")
    
        # 更新频谱仪状态
        spectrum_analyzer_status = test_results.get('spectrum_analyzer', None)
        if spectrum_analyzer_status is True:
            self.ui.labelSpectrumAnalyzer.setStyleSheet(onlineStyle)
            self.ui.labelSpectrumAnalyzer.setText("频谱仪: 在线")
        elif spectrum_analyzer_status is False:
            self.ui.labelSpectrumAnalyzer.setStyleSheet(offlineStyle)
            self.ui.labelSpectrumAnalyzer.setText("频谱仪: 离线")
        else:
            self.ui.labelSpectrumAnalyzer.setStyleSheet(unknownStyle)
            # self.ui.labelSpectrumAnalyzer.setText("频谱仪: 未检测")
            self.ui.labelSpectrumAnalyzer.setText("频谱仪: 检测中")

        # 更新矢量信号发生器状态
        signal_generator_status = test_results.get('signal_generator', None)
        if signal_generator_status is True:
            self.ui.labelSignalGenerator.setStyleSheet(onlineStyle)
            self.ui.labelSignalGenerator.setText("矢量信号发生器: 在线")
        elif signal_generator_status is False:
            self.ui.labelSignalGenerator.setStyleSheet(offlineStyle)
            self.ui.labelSignalGenerator.setText("矢量信号发生器: 离线")
        else:
            self.ui.labelSignalGenerator.setStyleSheet(unknownStyle)
            # self.ui.labelSignalGenerator.setText("矢量信号发生器: 未检测")
            self.ui.labelSignalGenerator.setText("矢量信号发生器: 检测中")
    
        # 更新蓝牙综测仪状态
        bluetooth_tester_status = test_results.get('bluetooth_tester', None)
        if bluetooth_tester_status is True:
            self.ui.labelBluetoothTester.setStyleSheet(onlineStyle)
            self.ui.labelBluetoothTester.setText("蓝牙综测仪: 在线")
        elif bluetooth_tester_status is False:
            self.ui.labelBluetoothTester.setStyleSheet(offlineStyle)
            self.ui.labelBluetoothTester.setText("蓝牙综测仪: 离线")
        else:
            self.ui.labelBluetoothTester.setStyleSheet(unknownStyle)
            # self.ui.labelBluetoothTester.setText("蓝牙综测仪: 未检测")
            self.ui.labelBluetoothTester.setText("蓝牙综测仪: 检测中")
    
    def closeEvent(self, event):
        # 在关闭对话框时返回数据
        self.setting_data['save_report_path'] = self.ui.lineEditSaveReportPath.text()
        self.setting_data['uart_port'] = self.ui.comboBoxSerialPort.currentText()
        self.setting_data['line_loss'] = self.ui.lineEditLineLoss.text()
        # 更新数据为标签的状态
        self.setting_data['dc_power'] = self.get_status_from_label(self.ui.labelDcPower.text())
        self.setting_data['spectrum_analyzer'] = self.get_status_from_label(self.ui.labelSpectrumAnalyzer.text())
        self.setting_data['signal_generator'] = self.get_status_from_label(self.ui.labelSignalGenerator.text())
        self.setting_data['bluetooth_tester'] = self.get_status_from_label(self.ui.labelBluetoothTester.text())
        self.done(QtWidgets.QDialog.Accepted)       # 关闭一个模态对话框，向父窗口发送一个 Accepted 信号，表示对话框已被接受
    
    def get_status_from_label(self, label_text):
        if "在线" in label_text:
            return True
        elif "离线" in label_text:
            return False
        else:
            return None
    

