"""
引擎参数
from logging import CRITICAL,ERROR,WARN,INFO,DEBUG # 导入日志等级
log_param = {
    "log_level": INFO,
    "log_console": True,
    "log_file": True,
    "log_file_dir": "log",
}
main_engine.add_engine(LogEngine, log_param) #加载命令引擎,指定参数

log_level: 设置日志等级，当输出log低于设置等级不输出
log_console: 是否log输出到终端
log_file : 是否输出到文件
log_file_dir: 当log_file配置成True时，这个参数指定输出文件的目录


引擎订阅事件
EVENT_LOG   # 写日志事件
参数：LogData(msg: str, level: int = logging.INFO)
    msg:  日志字符串
    level: 日志输出等级

EVENT_LOG_SET_DIR # 设置日志目录后缀
参数：LogDirData(log_dir: str):
    log_dir: 日志目录后缀

"""

import os
import time
import logging
from logging import CRITICAL, ERROR, WARN, INFO, DEBUG
from logging.handlers import RotatingFileHandler
from datetime import datetime
from gxpy.gxengine.eventengine import Event, EventEngine, BaseData
from gxpy.gxengine.baseengine import BaseEngine
from gxpy.gxengine.mainengine import MainEngine
from gxpy.gxengine.common import get_folder_path

EVENT_LOG = "eLog"
EVENT_LOG_SET_DIR = "eLogSetDir"


class LogData(BaseData):
    """
    Log data is used for recording log messages on GUI or in log files.
    """
    def __init__(self, msg: str, level: int = logging.INFO):
        self.msg = msg
        self.level = level


class LogDirData(BaseData):
    """
    Log data is used for recording log messages on GUI or in log files.
    """
    def __init__(self, log_dir: str):
        self.log_dir = log_dir


class TimestampedRotatingFileHandler(logging.handlers.RotatingFileHandler):
    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=0, utc=False):
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)
        self.utc = utc

    def _rotate(self, source, dest):
        # 关闭源文件句柄，确保文件被关闭
        if self.stream:
            self.stream.close()
            self.stream = None

        report_name, report_suffix = os.path.splitext(source)
        # 在文件名后面加上时间戳后缀
        timestamp = int(time.time())
        timestamp_str = time.strftime('%Y%m%d_%H%M%S', time.gmtime(timestamp) if self.utc else time.localtime(timestamp))
        dest_with_timestamp = '{}_{}{}'.format(report_name, timestamp_str, report_suffix)

        # 如果目标文件已存在，先将其重命名
        if os.path.exists(dest_with_timestamp):
            os.remove(dest_with_timestamp)

        # 重命名源文件
        os.rename(source, dest_with_timestamp)

        if not self.delay:
            self.stream = self._open()

    def doRollover(self):
        self._rotate(self.baseFilename, self.baseFilename)


class LogEngine(BaseEngine):
    """
    Processes log event and output with logging module.
    """

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine, param: dict):
        """"""
        super(LogEngine, self).__init__(main_engine, event_engine, "log")


        self.log_level = param["log_level"]
        self.log_console = param["log_console"]
        self.log_file = param["log_file"]
        if self.log_file:
            self.log_file_dir = param["log_file_dir"]

        self.today_date = datetime.now().strftime("%Y%m%d")
        self.log_suffix_dir = ""

        self.logger = logging.getLogger("LogEngine")
        self.logger.setLevel(self.log_level)

        self.formatter = logging.Formatter(
            "%(asctime)s  %(levelname)s: %(message)s"
        )

        self.add_null_handler()

        if self.log_console:
            self.add_console_handler()

        if self.log_file:
            self.add_file_handler()

        self.register_event()

    def add_null_handler(self):
        """
        Add null handler for logger.
        """
        null_handler = logging.NullHandler()
        self.logger.addHandler(null_handler)

    def add_console_handler(self):
        """
        Add console output of log.
        """
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(self.formatter)
        self.logger.addHandler(console_handler)

    def add_file_handler(self):
        """
        Add file output of log.
        """
        filename = "log_%s.txt" % self.today_date
        log_path = get_folder_path(self.log_file_dir)
        file_path = os.path.join(log_path, filename)

        self.file_handler = logging.FileHandler(
            file_path, mode="a", encoding="utf8"
        )
        self.file_handler.setLevel(self.log_level)
        self.file_handler.setFormatter(self.formatter)
        self.logger.addHandler(self.file_handler)

    def reset_file_handler(self):
        """
        reset file output of log.
        """
        self.logger.removeHandler(self.file_handler)
        filename = "log.txt"
        log_path = get_folder_path(self.log_file_dir+"/" + self.log_suffix_dir)
        file_path = os.path.join(log_path, filename)
        self.file_handler = TimestampedRotatingFileHandler(file_path, mode="a", maxBytes=5*1024*1024, backupCount=0, encoding="utf8")
        self.file_handler.setLevel(self.log_level)
        self.file_handler.setFormatter(self.formatter)
        self.logger.addHandler(self.file_handler)

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_LOG, self.process_log_event)
        self.event_engine.register(EVENT_LOG_SET_DIR, self.process_log_set_dir_event)

    def process_log_set_dir_event(self, event: Event):
        self.log_suffix_dir = event.data.log_dir
        self.reset_file_handler()

    def process_log_event(self, event: Event):
        """
        Process log event.
        """
        log = event.data
        self.logger.log(log.level, log.msg)


