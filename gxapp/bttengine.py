# ***Begin***
import matplotlib
from gxpy.common.gxmessagepack import GxMessageData, GxMessagePack
# ***End***
# 以上为打包必要库，请勿动

import ctypes
import inspect
import time
import threading
import logging

from queue import Queue, Empty
from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.styles import Ali<PERSON><PERSON>, Font, PatternFill
from openpyxl.styles import Border, Side, colors
from openpyxl.utils import get_column_letter, column_index_from_string

from gxpy.gxengine.eventengine import Event, EventEngine, BaseData
from gxpy.gxengine.baseengine import BaseEngine
from gxpy.gxengine.mainengine import MainEngine

from gxapp.device_id_info import btt_id_list
from gxapp.logengine import LogData, EVENT_LOG
from gxapp.bttengine_run_data import (
    EVENT_BTT_RUN_BTT,
    EVENT_BTT_KILL_PROCESS,
    EVENT_BTT_RUNTIME_LOG,
    EVENT_BTT_FINISH_LOG,

    BttRunData,
    BttRunTimeLogData,
    BttKillProcessData,
    BttFinishLogData
)
from gxapp.device_work import DeviceWork


class BttWork(threading.Thread):
    def __init__(self, data: BttRunData, event_engine: EventEngine):
        threading.Thread.__init__(self)
        self.event_engine = event_engine
        self.data = data

        self.__lock = False
        self.__process_kill_flag = False

        self.tc_index = 0
        self.select_tc_set = data["test_case"]

    def write_error_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.ERROR)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_info_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.INFO)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_debug_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.DEBUG)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_finish_log(self, msg: str, result: bool = True):
        """
        Put finish log event with specific message.
        """
        if self.__process_kill_flag:
            return
        log = BttFinishLogData(msg=msg, result=result)
        event = Event(EVENT_BTT_FINISH_LOG, log)
        self.event_engine.put(event)

    def write_run_time_log(self):
        """
        Put run time log event with specific message.
        """
        log = BttRunTimeLogData(test_case_total=self.tc_total, test_case_index=self.tc_index)
        event = Event(EVENT_BTT_RUNTIME_LOG, log)
        self.event_engine.put(event)

    def get_select_tc_total_numb(self, tc: dict):
        """
        test_case = {
            "RF发射测试": [
                {"name":"RF发射测试1", "file":"./test/test1.py"},
                {"name":"RF发射测试3", "file":"./test/test3.py"}
                ],
            "RF接收测试": [
                {"name":"RF接收测试3", "file":"./test/test3.py"}
                ],
            }
        """
        total = 0
        for k, v in tc.items():
            total += len(v)
        return total

    def btt_test(self):
        self.tc_total = self.get_select_tc_total_numb(tc=self.select_tc_set)
        if self.tc_index > self.tc_total:
            return
        self.write_run_time_log()
        self.write_info_log("测试进度：{}/{}".format(self.tc_index, self.tc_total))

        for tc_set_name, tc_set in self.select_tc_set.items():
            self.write_info_log(msg="（{}）用例集的用例执行开始".format(tc_set_name))
            for tc in tc_set:
                tc_name = tc["name"]
                tc_file = tc["file"]
                self.write_info_log(msg="当前执行的用例为：{}".format(tc_name))
                dw = DeviceWork(data=self.data, cur_tc=tc_file, event_engine=self.event_engine)
                ret, error = dw.do_case()
                if ret is False:
                    self.write_finish_log(msg=error, result=False)
                    return
                self.write_info_log(msg="当前用例 {} 执行完成！".format(tc_name))
                self.tc_index += 1
                self.write_run_time_log()
                self.write_info_log("测试进度：{}/{}".format(self.tc_index, self.tc_total))
                time.sleep(5)
            self.write_info_log(msg="（{}）用例集的用例执行结束！".format(tc_set_name))
        self.write_finish_log(msg='', result=True)

    def run(self):
        """"""
        try:
            self.btt_test()
        except Exception as e:
            self.write_finish_log(msg="测试用例执行异常( " + str(e) + " )", result=False)
            raise Exception
            # return

    def __async_raise(self, tid, exctype):
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")

    def process_kill(self):
        if self.is_alive():
            while True:
                if self.__lock is False:
                    self.__process_kill_flag = True
                    self.__async_raise(self.ident, SystemExit)
                    break
                time.sleep(0.01)


class BttEngine(BaseEngine):
    """"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine, param: dict):
        """"""
        super().__init__(main_engine, event_engine, "btt")

        self.event_engine = event_engine
        self.queue = Queue()
        self.thread = threading.Thread(target=self.run)
        self.work = None
        self.active = False
        self.register_event()

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_BTT_RUN_BTT, self.process_run_btt_event)
        self.event_engine.register(EVENT_BTT_KILL_PROCESS, self.process_kill_event)

    def process_run_btt_event(self, event: Event):
        """
        Process run btt event.
        """
        data = event.data.data
        # Start engine when run first btt.
        if not self.active:
            self.start()

        self.queue.put(data)

    def run(self):
        """"""
        while self.active:
            try:
                data = self.queue.get(timeout=1)
                if self.work:
                    self.work.process_kill()
                self.work = BttWork(data, self.event_engine)
                self.work.start()
            except Empty:
                continue

    def process_kill_event(self, event: Event):
        if self.work:
            try:
                self.work.process_kill()
            except ValueError:
                pass
            except SystemError:
                pass
            except SystemExit:
                pass

            self.work = None

    def close(self):
        """"""
        self.active = False
        if self.work:
            self.work.process_kill()
        if self.thread.is_alive():
            self.thread.join()

    def start(self):
        """"""
        self.active = True
        self.thread.start()

