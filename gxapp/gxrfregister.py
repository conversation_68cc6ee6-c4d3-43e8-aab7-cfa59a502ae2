from PyQt5 import QtWidgets, QtCore, uic
from PyQt5.QtWidgets import QFileDialog
import serial.tools.list_ports
import os
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import <PERSON>FileDialog, QMainWindow, QMessageBox, QApplication
from gxpy.gxengine.eventengine import Event, EventEngine, BaseData, EVENT_TIMER
from gxpy.gxengine.common import resource_path

from gxpy.gxengine.uartmsgengine import (
    EVENT_MSG_RUN_UART,
    EVENT_MSG_RUN_UART_RESULT,
    EVENT_MSG_STOP_UART,
    EVENT_MSG_SEND_MSG,
    EVENT_MSG_SEND_MSG_RESULT,
    EVENT_MSG_RECV_MSG,
    MsgRunUartData,
    GxMessageData,
    GxMessagePack,
)

import gxapp.rg_data as rg


class GxRfRegister(QtWidgets.QDialog):
    write_signal = pyqtSignal()
    uart_signal = pyqtSignal()

    def __init__(self, event_engine: EventEngine, uart_port):
        super(GxRfRegister, self).__init__()
        self.uart_port = uart_port
        self.event_engine = event_engine
        self.register_event()
        self.init_ui()
        self.timer_read = QtCore.QTimer()
        self.timer_write = QtCore.QTimer()
        self.timer_write_all = QtCore.QTimer()
        self.timer_read_all = QtCore.QTimer()
        self.timer_write_all.timeout.connect(self.send_msg)
        self.timer_read_all.timeout.connect(self.rec_msg)
        self.timer_read.timeout.connect(self.timeout_cancle_read)
        self.timer_write.timeout.connect(self.timeout_cancle_write)
        self.write_signal.connect(self.recv_msg)
        self.uart_signal.connect(self.uart_run_result)
        self.item = 0
        self.flag_write = 0
        self.flag_read = False
        self.data = [0, 0, 0, 0, 0, 0, 0, 0]
        # 当前寄存器值
        self.file_content = []
        # 文件的数据
        self.stat = False
        # 用于长时间的操作状态，如果是0则空闲 ；1表示在占用，此时无法进行一些写入读取操作
        self.content = bytearray(8)
        self.num = 0x00
        self.pushButtonStat_green = "QPushButton{background-color: rgb(0, 255, 0); color: rgb(255, 0, 0);" \
                                    "border-radius: 20px;  border: 2px groove gray; border-style: outset;}"
        self.pushButtonStat_red = "QPushButton{background-color: rgb(255, 0, 0); color: rgb(255, 0, 0);" \
                                       "border-radius: 20px;  border: 2px groove gray; border-style: outset;}"
        self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
        self.address_list = sorted(rg.REG_DATA.keys(), reverse=False)
        self.ui.lineEditAdressInput.setText(self.address_list[0])
        self.port_open()

    def init_ui(self):
        self.ui = uic.loadUi(resource_path('gxapp/ui/rf_register.ui'), self)
        self.setWindowTitle("寄存器读写")
        self.setWindowFlags(QtCore.Qt.WindowCloseButtonHint)
        self.setFixedSize(self.width(), self.height())
        self.setModal(True)

        self.ui.pushButtonBit0.clicked.connect(self.data_bit_0)
        self.ui.pushButtonBit1.clicked.connect(self.data_bit_1)
        self.ui.pushButtonBit2.clicked.connect(self.data_bit_2)
        self.ui.pushButtonBit3.clicked.connect(self.data_bit_3)
        self.ui.pushButtonBit4.clicked.connect(self.data_bit_4)
        self.ui.pushButtonBit5.clicked.connect(self.data_bit_5)
        self.ui.pushButtonBit6.clicked.connect(self.data_bit_6)
        self.ui.pushButtonBit7.clicked.connect(self.data_bit_7)
        self.ui.pushButtonWrite.clicked.connect(self.data_write)
        self.ui.pushButtonRead.clicked.connect(self.data_read)
        self.ui.pushButtonFileOpen.clicked.connect(self.file_open)
        self.ui.pushButtonExport.clicked.connect(self.data_read_all)
        self.ui.pushButtonInputAll.clicked.connect(self.file_input)
        self.ui.pushButtonBit0.installEventFilter(self)
        self.ui.pushButtonBit1.installEventFilter(self)
        self.ui.pushButtonBit2.installEventFilter(self)
        self.ui.pushButtonBit3.installEventFilter(self)
        self.ui.pushButtonBit4.installEventFilter(self)
        self.ui.pushButtonBit5.installEventFilter(self)
        self.ui.pushButtonBit6.installEventFilter(self)
        self.ui.pushButtonBit7.installEventFilter(self)
        self.ui.lineEditStat.installEventFilter(self)
        self.ui.lineEditAdressInput.installEventFilter(self)

    def eventFilter(self, object, event):
        if event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit0:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[0])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit1:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[1])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit2:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[2])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit3:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[3])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit4:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[4])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit5:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[5])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit6:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[6])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.HoverEnter and object == self.ui.pushButtonBit7:
            self.ui.textBrowserInformation.clear()
            try:
                self.ui.textBrowserInformation.append(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[7])
            except Exception as e:
                pass
            return True

        elif event.type() == QtCore.QEvent.KeyRelease and object == self.ui.lineEditAdressInput:

            input_adress = self.ui.lineEditAdressInput.text()
            cnt = len(self.address_list)
            if len(input_adress) > 10:
                self.ui.lineEditAdressInput.setEnabled(False)
                self.show_error_message('error data', '请按格式输入地址!\t\n如：0x0100003C')
            elif len(input_adress) == 10:
                try:
                    n = int(input_adress, 16)
                    for i in range(0, cnt):
                        if n == int(self.address_list[i], 16):
                            self.ui.lineEditAdressInput.clear()
                            self.ui.lineEditAdressInput.setText(self.address_list[i])
                            num = int(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8], 16)
                            for i in range(0, 8):
                                self.data[i] = num >> i & 0x01
                            self.adress_change()
                            num = self.text_to_stat()
                            self.ui.lineEditStat.setText(str(hex(num)))
                            return True
                    self.ui.lineEditAdressInput.setEnabled(False)
                    self.show_error_message('error data', '没有这个地址!\t')
                except ValueError:
                    self.ui.lineEditAdressInput.setEnabled(False)
                    self.show_error_message('error data', '请按格式输入地址!\t\n如：0x0100003C')
                    return False

            return True

        elif event.type() == QtCore.QEvent.KeyRelease and object == self.ui.lineEditStat:
            # 此处写更改寄存器状态
            input_s = self.ui.lineEditStat.text()
            while input_s != '' and len(input_s) > 2:
                if len(input_s) >= 5:
                    self.ui.lineEditStat.setEnabled(False)
                    self.show_error_message('error data', '请输入八位的十六进制数据!\t')
                    break
                try:
                    num = int(input_s[0:4], 16)
                    try:
                        eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
                    except Exception as e:
                        pass
                    for i in range(0, 8):
                        self.data[i] = num >> i & 0x01
                    self.adress_change()
                    break
                except ValueError:
                    self.ui.lineEditStat.setEnabled(False)
                    self.show_error_message('error data', '请输入八位的十六进制数据!\t')
                    return False

        self.ui.lineEditAdressInput.setEnabled(True)
        self.ui.lineEditStat.setEnabled(True)
        return False

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_MSG_RUN_UART_RESULT, self.process_msg_run_uart_result_event)
        self.event_engine.register(EVENT_MSG_SEND_MSG_RESULT, self.process_msg_send_msg_result_event)
        self.event_engine.register(EVENT_MSG_RECV_MSG, self.process_msg_recv_msg_event)

    def unregister_event(self):
        """"""
        self.event_engine.unregister(EVENT_MSG_RUN_UART_RESULT, self.process_msg_run_uart_result_event)
        self.event_engine.unregister(EVENT_MSG_SEND_MSG_RESULT, self.process_msg_send_msg_result_event)
        self.event_engine.unregister(EVENT_MSG_RECV_MSG, self.process_msg_recv_msg_event)

    def process_msg_run_uart_result_event(self, event: Event):
        data = event.data
        self.data_content = data
        self.uart_signal.emit()

    def process_msg_send_msg_result_event(self, event: Event):
        data = event.data

    def process_msg_recv_msg_event(self, event: Event):

        data = event.data
        self.data_content = data
        self.write_signal.emit()

    def show_error_message(self, title: str, message: str, detailed_msg: str = None):
        mb = QMessageBox()
        mb.setIcon(QMessageBox.Critical)
        mb.setWindowTitle(title)
        mb.setText(message)
        mb.setStandardButtons(QMessageBox.Cancel)
        buttonX = mb.button(QMessageBox.Cancel)
        buttonX.setText("OK")
        if detailed_msg != None:
            mb.setDetailedText(detailed_msg)
        mb.exec()

    def uart_run_result(self):
        data = self.data_content
        if data.result:
            pass
        else:
            self.show_error_message("串口打开失败", self.uart_port+" 串口打开失败", data.msg)

    def recv_msg(self):
        data = self.data_content
        # 单个写入数据处理
        if data.cmd == 0x04 and data.content[0] == int(self.ui.lineEditAdressInput.text()[0:4], 16) and \
                data.content[3] == int(self.ui.lineEditAdressInput.text()[8:11], 16) and self.flag_write == 1:

            self.flag_write = 0
            if self.stat == False:
                self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)

            self.ui.lineEditStat.setText(hex(data.content[7]))
            num = data.content[7]
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(data.content[7])
            for i in range(0, 8):
                self.data[i] = num >> i & 0x01
            self.adress_change()
        # 批量写入数据处理
        elif data.cmd == 0x04 and data.content[0] == self.content[0] and data.content[3] == self.content[3] \
                and self.flag_write == 3:

            self.flag_write = 0
            self.time_cnt = 0
            self.ui.lineEditStat.setText(hex(data.content[7]))
            num = data.content[7]
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(data.content[7])
            for i in range(0, 8):
                self.data[i] = num >> i & 0x01
            self.adress_change()
        # 读数据处理
        elif data.cmd == 0x02 and data.content[0] == int(self.ui.lineEditAdressInput.text()[0:4], 16) and \
                data.content[3] == int(self.ui.lineEditAdressInput.text()[8:11], 16) and self.flag_read == True:

            self.flag_read = False
            self.time_cnt = 0

            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.ui.lineEditStat.setText(hex(data.content[7]))
            num = data.content[7]
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(data.content[7])
            for i in range(0, 8):
                self.data[i] = num >> i & 0x01
            self.adress_change()

        # 批量写地址出错
        elif data.cmd == 0x04 and data.content[0] == self.content[0] and data.content[3] != self.content[3] \
                and self.flag_write == 3:

            self.flag_write = 0
            self.time_cnt = 0
            self.stat = False
            self.timer_write_all.stop()
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.show_error_message('error', '写操作收到地址与发出地址不一致 ')

        # 读地址不一致
        elif data.cmd == 0x02 and data.content[0] == int(self.ui.lineEditAdressInput.text()[0:4], 16) and \
                data.content[3] != int(self.ui.lineEditAdressInput.text()[8:11], 16) and self.flag_read == True:
            self.stat = False
            self.flag_read = False
            self.time_cnt = 0
            self.cnt = 0

            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.timer_read.stop()
            self.timer_read_all.stop()
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.show_error_message('error', '读操作收到地址与发出地址不一致 ')

        # 写地址不一致
        elif data.cmd == 0x04 and data.content[0] == int(self.ui.lineEditAdressInput.text()[0:4], 16) and \
                data.content[3] != int(self.ui.lineEditAdressInput.text()[8:11], 16) and self.flag_write == 1:

            self.flag_write = 0
            if self.stat == False:
                self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)

            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.timer_write.stop()
            self.show_error_message('error', '写操作收到地址与发出地址不一致 ')

    def adress_change(self):
        self.ui.pushButtonBit0.setText(str(self.data[0]))
        self.ui.pushButtonBit1.setText(str(self.data[1]))
        self.ui.pushButtonBit2.setText(str(self.data[2]))
        self.ui.pushButtonBit3.setText(str(self.data[3]))
        self.ui.pushButtonBit4.setText(str(self.data[4]))
        self.ui.pushButtonBit5.setText(str(self.data[5]))
        self.ui.pushButtonBit6.setText(str(self.data[6]))
        self.ui.pushButtonBit7.setText(str(self.data[7]))
        self.ui.pushButtonBit0.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit1.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit2.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit3.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit4.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit5.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit6.setStyleSheet("QPushButton{}")
        self.ui.pushButtonBit7.setStyleSheet("QPushButton{}")

    def run_uart_event(self):
        """
        检测所有存在的串口，将信息存储在字典中
        """
        self.Com_Dict = {}
        port_list = list(serial.tools.list_ports.comports())
        self.ui.comboBoxCom.clear()
        for port in port_list:
            self.Com_Dict["%s" % port[0]] = "%s" % port[1]
            self.ui.comboBoxCom.addItem(port[0])

    def data_bit_0(self):
        self.ui.pushButtonBit0.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[0] == 0:
            self.ui.pushButtonBit0.setText("1")
            self.data[0] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit0.setText("0")
            self.data[0] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_1(self):
        self.ui.pushButtonBit1.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[1] == 0:
            self.ui.pushButtonBit1.setText("1")
            self.data[1] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit1.setText("0")
            self.data[1] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_2(self):
        self.ui.pushButtonBit2.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[2] == 0:
            self.ui.pushButtonBit2.setText("1")
            self.data[2] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit2.setText("0")
            self.data[2] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_3(self):
        self.ui.pushButtonBit3.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[3] == 0:
            self.ui.pushButtonBit3.setText("1")
            self.data[3] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit3.setText("0")
            self.data[3] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_4(self):
        self.ui.pushButtonBit4.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[4] == 0:
            self.ui.pushButtonBit4.setText("1")
            self.data[4] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit4.setText("0")
            self.data[4] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_5(self):
        self.ui.pushButtonBit5.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[5] == 0:
            self.ui.pushButtonBit5.setText("1")
            self.data[5] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit5.setText("0")
            self.data[5] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_6(self):
        self.ui.pushButtonBit6.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[6] == 0:
            self.ui.pushButtonBit6.setText("1")
            self.data[6] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit6.setText("0")
            self.data[6] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def data_bit_7(self):
        self.ui.pushButtonBit7.setStyleSheet("QPushButton{background-color: rgb(85, 255, 255);}")
        if self.data[7] == 0:
            self.ui.pushButtonBit7.setText("1")
            self.data[7] = 1
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        else:
            self.ui.pushButtonBit7.setText("0")
            self.data[7] = 0
            num = self.text_to_stat()
            self.ui.lineEditStat.setText(str(hex(num)))
        try:
            eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8] = hex(num)
        except Exception as e:
            pass

    def port_open(self):
        d = MsgRunUartData(self.uart_port, 115200)
        event = Event(EVENT_MSG_RUN_UART, d)
        self.event_engine.put(event)

    def port_close(self):
        event = Event(EVENT_MSG_STOP_UART)
        self.event_engine.put(event)

    def data_read(self):
        if self.flag_read == False and self.flag_write == 0:
            self.flag_read = True
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_red)
            self.ui.pushButtonBit0.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit1.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit2.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit3.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit4.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit5.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit6.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit7.setStyleSheet("QPushButton{}")
            msg_cmd = 0x0001
            msg_type = 0x01
            msg_seq = 0x00
            msg_flags = 0x00
            msg_content = bytearray(8)
            # 0-3 状态 ，4-7 地址
            msg_content[0] = int(self.ui.lineEditAdressInput.text()[0:4], 16)
            msg_content[3] = int(self.ui.lineEditAdressInput.text()[8:11], 16)
            msg_content[7] = 0x01
            d = GxMessageData(msg_cmd, msg_type, msg_seq, msg_flags, msg_content)
            self.send_data(d)

            if not self.timer_read_all.isActive():
                self.timer_read.setSingleShot(True)
                self.timer_read.start(5000)

    def timeout_cancle_read(self):
        if self.flag_read == True:
            self.stat = False
            self.flag_read = False
            self.flag_write = 0
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.show_error_message('Timeout', 'Connection timed out ')

    def data_write(self):
        if self.flag_write == 0 and self.flag_read == False:
            self.flag_write = 1
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_red)
            self.ui.pushButtonBit0.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit1.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit2.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit3.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit4.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit5.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit6.setStyleSheet("QPushButton{}")
            self.ui.pushButtonBit7.setStyleSheet("QPushButton{}")
            try:
                self.num = int(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8], 16)
            except Exception as e:
                pass
            msg_cmd = 0x0003
            msg_type = 0x01
            msg_seq = 0x00
            msg_flags = 0x00
            msg_content = bytearray(8)
            msg_content[0] = int(self.ui.lineEditAdressInput.text()[0:4], 16)
            msg_content[3] = int(self.ui.lineEditAdressInput.text()[8:11], 16)
            msg_content[7] = self.num

            d = GxMessageData(msg_cmd, msg_type, msg_seq, msg_flags, msg_content)
            self.send_data(d)
            self.timer_write.setSingleShot(True)
            self.timer_write.start(5000)

    def timeout_cancle_write(self):
        if self.flag_write == 1:
            self.stat = False
            self.flag_read = False
            self.flag_write = 0
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.show_error_message('Timeout', 'Connection timed out ')

    def file_open(self):
        # 其中self指向自身，"读取文件夹"为标题名，"./"为打开时候的当前路径
        if self.flag_write == 0 and self.stat == False and self.flag_read == False:
            self.ui.lineEditFilePath.setText("")
            self.file_path, self.file_type = QFileDialog.getOpenFileName(self, "选取文件", os.path.abspath('.'), "Text Files (*.txt)")  # 起始路径
            if self.file_path != "":
                self.ui.lineEditFilePath.setText(self.file_path)

    def file_export(self):
        # 保存数据text
        f = open(self.file_path, 'w')
        self.count = len(self.address_list)
        self.item = 0
        self.ui.lineEditAdressInput.setText(self.address_list[0])
        for i in range(0, self.count):
            self.ui.lineEditAdressInput.setText(self.address_list[self.item])
            print(f.write('{},'.format(self.ui.lineEditAdressInput.text())))
            print(f.write('{}\n'.format(eval(rg.REG_DATA[self.ui.lineEditAdressInput.text()])[8])))
            self.item = self.item + 1

        f.close()
        self.ui.lineEditAdressInput.setText(self.address_list[0])
        self.stat = False
        self.ui.pushButtonWrite.setEnabled(True)
        self.ui.pushButtonRead.setEnabled(True)

    def file_input(self):
        if self.flag_write == 0 and self.stat == False and self.flag_read == False:
            input_s = self.ui.lineEditFilePath.text()
            self.file_content.clear()

            if input_s != '':
                try:
                    with open(self.file_path, 'r') as file:

                        for i, line in enumerate(file, 1):
                            if line[0] != '#' and line != '\n':
                                string = str(line)
                                self.file_content.append(string)
                            else:
                                pass

                except Exception as e:
                    pass

                self.count = len(self.file_content)
                self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_red)
                self.ui.pushButtonWrite.setEnabled(False)
                self.ui.pushButtonRead.setEnabled(False)
                self.stat = True
                self.item = 0
                self.time_cnt = 0
                self.cnt = 0

                self.timer_write_all.start(20)
            else:
                self.show_error_message('error', '请先选择文件!\t')

    def send_msg(self):
        if self.time_cnt == 251 and self.cnt == self.item and self.flag_write == 3:
            self.stat = False
            self.flag_write = 0
            self.time_cnt = 0
            self.cnt = 0
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.timer_write_all.stop()
            self.ui.lineEditAdressInput.setText(self.address_list[0])
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.show_error_message('Timeout', 'Connection timed out ')
            return
        if self.flag_write == 0 and self.item != self.count:
            msg_cmd = 0x0003
            msg_type = 0x01
            msg_seq = 0x00
            msg_flags = 0x00
            msg_content = bytearray(8)
            try:
                msg_content[0] = int(self.file_content[self.item][0:4], 16)
                msg_content[3] = int(self.file_content[self.item][8:10], 16)
                msg_content[7] = int(self.file_content[self.item][11:15], 16)
            except Exception as e:
                self.stat = False
                self.flag_write = 0
                self.time_cnt = 0
                self.cnt = 0
                self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
                self.timer_write_all.stop()
                self.ui.lineEditAdressInput.setText(self.address_list[0])
                self.ui.pushButtonWrite.setEnabled(True)
                self.ui.pushButtonRead.setEnabled(True)
                self.show_error_message('Error', '文件地址转换出错 ')
                return

            self.content[0] = msg_content[0]
            self.content[3] = msg_content[3]
            self.content[7] = msg_content[7]
            self.ui.lineEditAdressInput.setText(str(self.file_content[self.item][0:10]))
            cnt = len(self.address_list)
            for i in range(0, cnt):
                if int(self.file_content[self.item][0:10], 16) == int(self.address_list[i], 16):
                    self.ui.lineEditAdressInput.setText(self.address_list[i])
            d = GxMessageData(msg_cmd, msg_type, msg_seq, msg_flags, msg_content)
            self.send_data(d)
            self.item = self.item + 1
            self.flag_write = 3

            self.cnt = self.item
            self.time_cnt = 0

        if self.item == self.count and self.flag_write == 0:
            self.stat = False
            self.time_cnt = 0
            self.timer_write_all.stop()
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
        self.time_cnt = self.time_cnt + 1

    def text_to_stat(self):
        num = 0x00
        for i in range(0, 8):
            num += self.data[i] * (2 ** i)
        return num

    def data_read_all(self):
        if self.flag_read == False and self.stat == False and self.flag_write == 0:
            self.ui.lineEditFilePath.setText("")
            self.file_path, self.file_type = QFileDialog.getSaveFileName(self, "文件保存", "./", "Text Files (*.txt)")
            if self.file_path != '':
                self.stat = True
                self.ui.lineEditFilePath.setText(self.file_path)
                self.ui.pushButtonWrite.setEnabled(False)
                self.ui.pushButtonRead.setEnabled(False)
                self.count = len(self.address_list)
                self.item = 0
                self.ui.lineEditAdressInput.setText(self.address_list[0])
                self.time_cnt = 0
                self.cnt = 0

                self.timer_read_all.start(20)

    def rec_msg(self):
        # 读取寄存器值
        self.time_cnt = self.time_cnt + 1
        if self.time_cnt == 251 and self.cnt == self.item:
            self.stat = False
            self.flag_read = False
            self.time_cnt = 0
            self.cnt = 0
            self.ui.pushButtonStat.setStyleSheet(self.pushButtonStat_green)
            self.timer_read_all.stop()
            self.show_error_message("Timeout", 'Connection timed out')
            self.ui.lineEditAdressInput.setText(self.address_list[0])
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            return

        if self.flag_read == False and self.item != self.count -1:
            self.time_cnt = 0
            self.ui.lineEditAdressInput.setText(self.address_list[self.item])
            self.data_read()
            self.item = self.item + 1
            self.cnt = self.item

        elif self.item == self.count - 1 and self.flag_read == False:
            # 读取完成
            self.stat = False
            self.ui.lineEditAdressInput.setText(self.address_list[0])
            self.timer_read_all.stop()
            self.ui.pushButtonWrite.setEnabled(True)
            self.ui.pushButtonRead.setEnabled(True)
            self.file_export()

    def send_data(self, data):
        event = Event(EVENT_MSG_SEND_MSG, data)
        self.event_engine.put(event)


    def closeEvent(self, event):
        self.unregister_event()
        self.port_close()

