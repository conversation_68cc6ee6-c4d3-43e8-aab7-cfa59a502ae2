import gxapp.rc

from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets, uic
from PyQt5.QtWidgets import QLineEdit
from gxpy.gxengine.common import resource_path
from gxapp.version import version


class GxHelp(QtWidgets.QDialog):

    def __init__(self):
        super(Gx<PERSON>elp, self).__init__()
        self.ui = uic.loadUi(resource_path('gxapp/ui/help.ui'), self)
        self.setWindowTitle("使用帮助")
        self.setWindowFlags(QtCore.Qt.WindowCloseButtonHint | QtCore.Qt.WindowMaximizeButtonHint)
        self.setModal(True)
