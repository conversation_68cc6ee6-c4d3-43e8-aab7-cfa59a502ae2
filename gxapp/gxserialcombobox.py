import serial
import serial.tools.list_ports
from PyQt5.QtWidgets import QComboBox


class GxSerialComboBox(QComboBox):

    def __init__(self, parent=None):
        super(GxSerialComboBox, self).__init__(parent)

    def showPopup(self):
        self.update_serial_info()
        QComboBox.showPopup(self)   # 弹出选项框

    def update_serial_info(self):
        ports = []
        # 先清空原有的选项
        self.clear()
        for n, (portname, desc, hwid) in enumerate(sorted(serial.tools.list_ports.comports())):
            ports.append(portname)

        self.addItems(ports)