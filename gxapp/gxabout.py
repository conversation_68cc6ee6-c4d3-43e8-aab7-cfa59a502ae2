
from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets, uic
from PyQt5.QtWidgets import QLineEdit
from gxpy.gxengine.common import resource_path
from gxapp.version import version


class GxAbout(QtWidgets.QDialog):
    def __init__(self):
        """"""
        super(GxAbout, self).__init__()
        self.init_data()
        self.init_ui()

    def init_data(self):
        pass

    def init_ui(self):
        uic.loadUi(resource_path('gxapp/ui/about.ui'), self)
        self.setWindowFlags(QtCore.Qt.WindowCloseButtonHint)
        self.setFixedSize(self.width(), self.height())

        self.setWindowTitle("关于gxbtt")
        self.labelName.setText("蓝牙自动化测试工具")
        self.labelInfo.setText("杭州国芯微电子股份有限公司")
        self.labelVersion.setText('v'+version)



