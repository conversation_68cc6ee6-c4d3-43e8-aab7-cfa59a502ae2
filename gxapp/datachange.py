import os

from openpyxl import load_workbook

from gxapp.public_data import *


class DataChange:

    def __init__(self, filename: str):
        self.filename = filename

    def check_file_is_exist(self):
        if self.filename == '':
            return False, None
        if not os.path.exists(self.filename):
            error = "测试汇总文件不存在！"
            return False, error
        else:
            try:
                wb = load_workbook(self.filename)
            except Exception as e:
                return False, str(e)
        return True, wb

    def check_sheet_is_exist(self, sheet_name: str):
        ret, data = self.check_file_is_exist()
        if ret is False:
            return ret, data, None, None
        else:
            wb = data

        sheets_name = wb.sheetnames
        if sheet_name in sheets_name:
            ws = wb[sheet_name]
            return True, None, wb, ws
        else:
            error = "测试汇总文件中指定的[{}]表不存在！".format(sheet_name)
            wb.save(self.filename)
            return False, error, None, None

    def check_case_file_path_is_exist(self, case_info: dict):
        case_name = case_info["name"]
        case_file = case_info["file"]
        if os.path.exists(case_file):
            abs_case_file = os.path.abspath(case_file)
            return True, None, abs_case_file
        else:
            error = "{}，该用例文件路径不存在，请检查！".format(case_name)
            return False, error, None

    def get_case_for_sheet(self, sheet_name: str):
        single_sheet_case = list()
        ret, data, wb, ws = self.check_sheet_is_exist(sheet_name=sheet_name)
        if ret is False:
            wb.save(self.filename)
            return ret, data, single_sheet_case

        max_row = ws.max_row
        max_col = ws.max_column
        if max_col < 2 or max_col != 2:
            error = "测试汇总文件中指定的[{}]表数据不完整或内容错误，请检查！".format(sheet_name)
            wb.save(self.filename)
            return False, error, single_sheet_case

        title = ["name", 'file']

        for row_n in range(2, max_row + 1):
            single_row_case = dict()
            for col_n in range(len(title)):
                key = title[col_n]
                val = ws.cell(row_n, col_n + 1).value
                if val is None:
                    val = None
                elif val.strip() == "":
                    val = None
                else:
                    val = val.strip()
                single_row_case[key] = val
            single_sheet_case.append(single_row_case)
        wb.save(self.filename)

        print(single_sheet_case)
        case_info_error_flag = False
        case_info_error_info = list()
        handle_single_sheet_case = list()
        for case_info in single_sheet_case:
            handle_case_info = dict()
            case_name = case_info["name"]
            case_file = case_info["file"]
            if case_name is None and case_file is None:
                continue        # 过滤名称和用例文件都为空的用例
            elif case_name is None or case_file is None:
                case_info_error_flag = True
                error = "{}文件，[{}]表单下，有用例名称或用例对应的py文件为空，请检查！".format(
                    os.path.basename(self.filename), sheet_name)
                case_info_error_info.append(error)
            else:
                if not os.path.exists(case_file):
                    case_info_error_flag = True
                    error = "{}文件，[{}]表单下，该{}用例对应的py文件({})的路径错误，请检查！".format(
                        os.path.basename(self.filename), sheet_name, case_name, case_file)
                    case_info_error_info.append(error)
                else:
                    case_file_abs_path = os.path.abspath(case_file)
                    handle_case_info["name"] = case_name
                    handle_case_info["file"] = case_file_abs_path
            handle_single_sheet_case.append(handle_case_info)

        if case_info_error_flag:
            return False, case_info_error_info, handle_single_sheet_case
        return True, None, handle_single_sheet_case

    def read_test_case(self):
        test_case = list()
        # 解析测试汇总目录数据
        sht_name = "测试汇总"
        ret, data, wb, ws = self.check_sheet_is_exist(sheet_name=sht_name)
        if ret is False:
            return ret, data, test_case

        max_row = ws.max_row
        max_col = ws.max_column
        if max_col < 3 or max_col < 5:
            error = "测试汇总文件中指定的[{}]表数据不完整，请检查！".format(sht_name)
            wb.save(self.filename)
            return False, error, test_case

        title_name = list()
        for col_n in range(max_col):
            if col_n < 2:
                cell_val = ws.cell(1, col_n + 1).value
            else:
                cell_val = ws.cell(2, col_n + 1).value
            title_name.append(cell_val.strip())

        if title_name != test_summary_title:
            error = "测试汇总文件中指定的[{}]表头数据不正确，请检查！".format(sht_name)
            wb.save(self.filename)
            return False, error, test_case

        test_summary_data = list()
        for row_n in range(3, max_row + 1):     # 测试汇总表单数据从第3行开始
            single_case = dict()
            for col_n in range(len(title_name)):
                key = title_name[col_n]
                val = ws.cell(row_n, col_n + 1).value
                if val == '' or val is None:
                    val = None
                else:
                    val = val.strip()
                single_case[key] = val
            test_summary_data.append(single_case)
        wb.save(self.filename)

        # 解析不同类别的测试用例
        # print(test_summary_data)
        for data in test_summary_data:
            single_chip_case = dict()
            single_chip_case["chip"] = data["芯片及代号"]
            # single_chip_case["rf_config_file_path"] = data["RF配置文件"]
            single_chip_all_case = dict()
            for k, v in data.items():
                if v is None:
                    error = "请检查汇总文件，({})子表，({})数据为空！".format(sht_name, k)
                    wb.save(self.filename)
                    return False, error, test_case

                if k in test_summary_title[2:]:
                    if v not in ["Y", "y", "N", "n"]:
                        error = "请检查汇总文件，({})子表，({})数据标记错误，请使用'Y'或'N'进行标记！".format(sht_name, k)
                        wb.save(self.filename)
                        return False, error, test_case
                    if v == 'Y' or v == 'y':
                        ret, data, single_sheet_case = self.get_case_for_sheet(sheet_name=k)
                        if ret is False:
                            return False, data, test_case
                        single_chip_all_case[k] = single_sheet_case
            single_chip_case["test_case"] = single_chip_all_case
            test_case.append(single_chip_case)
            print(test_case)

        # # 检测每条用例的路径是否异常
        # for single_chip_case in test_case:
        #     single_chip_all_case = single_chip_case["test_case"]
        #     for k, v in single_chip_all_case.items():   # v == [{'name':'', 'file':''}, {'name':'', 'file':''}]
        #         for case_info in v:
        #             ret, error, abs_case_file = self.check_case_file_path_is_exist(case_info=case_info)
        #             if ret is False:
        #                 print(error)

        return True, None, test_case[0]
