REG_DATA = {
            '0x01000000': 'rg.PAGE1_NAME1',
            '0x01000001': 'rg.PAGE1_NAME2',
            '0x01000002': 'rg.PAGE1_NAME3',
            '0x01000003': 'rg.PAGE1_NAME4',
            '0x01000004': 'rg.PAGE1_NAME5',
            '0x01000005': 'rg.PAGE1_NAME6',
            '0x01000006': 'rg.PAGE1_NAME7',
            '0x01000007': 'rg.PAGE1_NAME8',
            '0x01000008': 'rg.PAGE1_NAME9',
            '0x01000009': 'rg.PAGE1_NAME10',
            '0x0100000A': 'rg.PAGE1_NAME11',
            '0x0100000B': 'rg.PAGE1_NAME12',
            '0x0100000C': 'rg.PAGE1_NAME13',
            '0x0100000D': 'rg.PAGE1_NAME14',
            '0x0100000E': 'rg.PAGE1_NAME15',
            '0x0100000F': 'rg.PAGE1_NAME16',
            '0x01000010': 'rg.PAGE1_NAME17',
            '0x01000011': 'rg.PAGE1_NAME18',
            '0x01000012': 'rg.PAGE1_NAME19',
            '0x01000013': 'rg.PAGE1_NAME20',
            '0x01000014': 'rg.PAGE1_NAME21',
            '0x01000015': 'rg.PAGE1_NAME22',
            '0x01000016': 'rg.PAGE1_NAME23',
            '0x01000017': 'rg.PAGE1_NAME24',
            '0x01000018': 'rg.PAGE1_NAME25',
            '0x01000019': 'rg.PAGE1_NAME26',
            '0x0100001A': 'rg.PAGE1_NAME27',
            '0x0100001B': 'rg.PAGE1_NAME28',
            '0x0100001C': 'rg.PAGE1_NAME29',
            '0x0100001D': 'rg.PAGE1_NAME30',
            '0x0100001E': 'rg.PAGE1_NAME31',
            '0x0100001F': 'rg.PAGE1_NAME32',
            '0x01000020': 'rg.PAGE1_NAME33',
            '0x01000021': 'rg.PAGE1_NAME34',
            '0x01000022': 'rg.PAGE1_NAME35',
            '0x01000023': 'rg.PAGE1_NAME36',
            '0x01000024': 'rg.PAGE1_NAME37',
            '0x01000025': 'rg.PAGE1_NAME38',
            '0x01000026': 'rg.PAGE1_NAME39',
            '0x01000027': 'rg.PAGE1_NAME40',
            '0x01000028': 'rg.PAGE1_NAME41',
            '0x01000029': 'rg.PAGE1_NAME42',
            # '0x0100002A': 'rg.PAGE1_NAME43',
            '0x0100002B': 'rg.PAGE1_NAME44',
            # '0x0100002C': 'rg.PAGE1_NAME45',
            '0x0100002D': 'rg.PAGE1_NAME46',
            '0x0100002E': 'rg.PAGE1_NAME47',
            '0x0100002F': 'rg.PAGE1_NAME48',
            '0x01000030': 'rg.PAGE1_NAME49',
            '0x01000031': 'rg.PAGE1_NAME50',
            '0x01000032': 'rg.PAGE1_NAME51',
            '0x01000033': 'rg.PAGE1_NAME52',
            '0x01000034': 'rg.PAGE1_NAME53',
            '0x01000035': 'rg.PAGE1_NAME54',
            '0x01000036': 'rg.PAGE1_NAME55',
            '0x01000037': 'rg.PAGE1_NAME56',
            '0x01000038': 'rg.PAGE1_NAME57',
            '0x01000039': 'rg.PAGE1_NAME58',
            '0x0100003A': 'rg.PAGE1_NAME59',
            '0x0100003B': 'rg.PAGE1_NAME60',
            '0x0100003C': 'rg.PAGE1_NAME61',
            '0x0100003D': 'rg.PAGE1_NAME62',
            '0x0100003E': 'rg.PAGE1_NAME63',
            '0x0100003F': 'rg.PAGE1_NAME64',
            '0x01000040': 'rg.PAGE1_NAME65',
            '0x01000041': 'rg.PAGE1_NAME66',
            '0x01000042': 'rg.PAGE1_NAME67',
            '0x01000043': 'rg.PAGE1_NAME68',
            '0x01000044': 'rg.PAGE1_NAME69',
            '0x01000045': 'rg.PAGE1_NAME70',
            '0x01000046': 'rg.PAGE1_NAME71',
            '0x01000047': 'rg.PAGE1_NAME72',
            '0x01000048': 'rg.PAGE1_NAME73',
            '0x01000049': 'rg.PAGE1_NAME74',
            '0x0100004A': 'rg.PAGE1_NAME75',
            '0x0100004B': 'rg.PAGE1_NAME76',
            '0x0100004C': 'rg.PAGE1_NAME77',
            '0x0100004D': 'rg.PAGE1_NAME78',
            '0x0100004E': 'rg.PAGE1_NAME79',
            '0x0100004F': 'rg.PAGE1_NAME80',
            '0x01000050': 'rg.PAGE1_NAME81',
            '0x01000051': 'rg.PAGE1_NAME82',
            '0x01000052': 'rg.PAGE1_NAME83',
            '0x01000053': 'rg.PAGE1_NAME84',
            '0x01000054': 'rg.PAGE1_NAME85',
            '0x01000055': 'rg.PAGE1_NAME86',
            '0x01000056': 'rg.PAGE1_NAME87',
            '0x01000057': 'rg.PAGE1_NAME88',
            '0x01000058': 'rg.PAGE1_NAME89',
            '0x01000059': 'rg.PAGE1_NAME90',
            '0x0100005A': 'rg.PAGE1_NAME91',
            '0x0100005B': 'rg.PAGE1_NAME92',
            '0x0100005C': 'rg.PAGE1_NAME93',
            '0x0100005D': 'rg.PAGE1_NAME94',
            '0x0100005E': 'rg.PAGE1_NAME95',
            '0x0100005F': 'rg.PAGE1_NAME96',
            '0x01000060': 'rg.PAGE1_NAME97',
            '0x01000061': 'rg.PAGE1_NAME98',
            '0x01000062': 'rg.PAGE1_NAME99',
            '0x01000063': 'rg.PAGE1_NAME100',
            '0x01000064': 'rg.PAGE1_NAME101',
            '0x01000065': 'rg.PAGE1_NAME102',
            '0x01000066': 'rg.PAGE1_NAME103',
            '0x01000067': 'rg.PAGE1_NAME104',
            '0x01000068': 'rg.PAGE1_NAME105',
            '0x01000069': 'rg.PAGE1_NAME106',
            '0x0100006A': 'rg.PAGE1_NAME107',
            '0x0100006B': 'rg.PAGE1_NAME108',
            '0x0100006C': 'rg.PAGE1_NAME109',
            '0x0100006D': 'rg.PAGE1_NAME110',
            '0x0100006E': 'rg.PAGE1_NAME111',
            '0x0100006F': 'rg.PAGE1_NAME112',
            '0x01000070': 'rg.PAGE1_NAME113',
            '0x01000071': 'rg.PAGE1_NAME114',
            '0x01000072': 'rg.PAGE1_NAME115',
            '0x01000073': 'rg.PAGE1_NAME116',
            '0x01000074': 'rg.PAGE1_NAME117',
            '0x01000075': 'rg.PAGE1_NAME118',
            '0x01000076': 'rg.PAGE1_NAME119',
            '0x01000077': 'rg.PAGE1_NAME120',
            '0x01000078': 'rg.PAGE1_NAME121',
            '0x01000079': 'rg.PAGE1_NAME122',
            '0x0100007A': 'rg.PAGE1_NAME123',
            '0x0100007B': 'rg.PAGE1_NAME124',
            '0x0100007C': 'rg.PAGE1_NAME125',
            '0x0100007D': 'rg.PAGE1_NAME126',
            '0x0100007E': 'rg.PAGE1_NAME127',
            '0x0100007F': 'rg.PAGE1_NAME128',
            '0x01000080': 'rg.PAGE1_NAME129',
            '0x01000081': 'rg.PAGE1_NAME130',
            '0x01000082': 'rg.PAGE1_NAME131',
            '0x01000083': 'rg.PAGE1_NAME132',
            '0x01000084': 'rg.PAGE1_NAME133',
            '0x01000085': 'rg.PAGE1_NAME134',
            '0x01000086': 'rg.PAGE1_NAME135',
            '0x01000087': 'rg.PAGE1_NAME136',
            '0x01000088': 'rg.PAGE1_NAME137',
            '0x01000089': 'rg.PAGE1_NAME138',
            '0x0100008A': 'rg.PAGE1_NAME139',
            '0x0100008B': 'rg.PAGE1_NAME140',
            '0x0100008C': 'rg.PAGE1_NAME141',
            '0x0100008D': 'rg.PAGE1_NAME142',
            '0x0100008E': 'rg.PAGE1_NAME143',
            '0x0100008F': 'rg.PAGE1_NAME144',
            '0x01000090': 'rg.PAGE1_NAME145',
            '0x01000091': 'rg.PAGE1_NAME146',
            '0x01000092': 'rg.PAGE1_NAME147',
            '0x01000093': 'rg.PAGE1_NAME148',
            '0x01000094': 'rg.PAGE1_NAME149',
            '0x01000095': 'rg.PAGE1_NAME150',
            '0x01000096': 'rg.PAGE1_NAME151',
            '0x01000097': 'rg.PAGE1_NAME152',
            '0x01000098': 'rg.PAGE1_NAME153',
            '0x01000099': 'rg.PAGE1_NAME154',
            '0x0100009A': 'rg.PAGE1_NAME155',
            '0x0100009B': 'rg.PAGE1_NAME156',
            '0x0100009C': 'rg.PAGE1_NAME157',
            # '0x0100009D': 'rg.PAGE1_NAME158',
            '0x0100009E': 'rg.PAGE1_NAME159',
            '0x0100009F': 'rg.PAGE1_NAME160',
            '0x010000A0': 'rg.PAGE1_NAME161',
            '0x010000A1': 'rg.PAGE1_NAME162',
            '0x010000A2': 'rg.PAGE1_NAME163',
            '0x010000A3': 'rg.PAGE1_NAME164',
            '0x010000A4': 'rg.PAGE1_NAME165',
            '0x010000A5': 'rg.PAGE1_NAME166',
            '0x010000A6': 'rg.PAGE1_NAME167',
            '0x010000A7': 'rg.PAGE1_NAME168',
            '0x010000A8': 'rg.PAGE1_NAME169',
            '0x010000A9': 'rg.PAGE1_NAME170',
            '0x010000AA': 'rg.PAGE1_NAME171',
            '0x010000AB': 'rg.PAGE1_NAME172',
            '0x010000AC': 'rg.PAGE1_NAME173',
            '0x010000AD': 'rg.PAGE1_NAME174',
            '0x010000AE': 'rg.PAGE1_NAME175',
            '0x010000AF': 'rg.PAGE1_NAME176',
            '0x010000B0': 'rg.PAGE1_NAME177',
            '0x010000B1': 'rg.PAGE1_NAME178',
            '0x010000B2': 'rg.PAGE1_NAME179',
            '0x010000B3': 'rg.PAGE1_NAME180',
            '0x010000B4': 'rg.PAGE1_NAME181',
            '0x010000B5': 'rg.PAGE1_NAME182',
            '0x010000B6': 'rg.PAGE1_NAME183',
            '0x010000B7': 'rg.PAGE1_NAME184',
            '0x010000B8': 'rg.PAGE1_NAME185',
            '0x010000B9': 'rg.PAGE1_NAME186',
            '0x010000BA': 'rg.PAGE1_NAME187',
            '0x010000BB': 'rg.PAGE1_NAME188',
            '0x010000BC': 'rg.PAGE1_NAME189',
            '0x010000BD': 'rg.PAGE1_NAME190',
            '0x010000BE': 'rg.PAGE1_NAME191',
            '0x010000BF': 'rg.PAGE1_NAME192',
            '0x010000C0': 'rg.PAGE1_NAME193',
            '0x010000C1': 'rg.PAGE1_NAME194',
            '0x010000C2': 'rg.PAGE1_NAME195',
            '0x010000C3': 'rg.PAGE1_NAME196',
            '0x010000C4': 'rg.PAGE1_NAME197',
            '0x010000C5': 'rg.PAGE1_NAME198',
            # '0x010000C6': 'rg.PAGE1_NAME199',
            # '0x010000C7': 'rg.PAGE1_NAME200',
            # '0x010000C8': 'rg.PAGE1_NAME201',
            # '0x010000C9': 'rg.PAGE1_NAME202',
            # '0x010000CA': 'rg.PAGE1_NAME203',
            # '0x010000CB': 'rg.PAGE1_NAME204',
            # '0x010000CC': 'rg.PAGE1_NAME205',
            # '0x010000CD': 'rg.PAGE1_NAME206',
            # '0x010000CE': 'rg.PAGE1_NAME207',
            # '0x010000CF': 'rg.PAGE1_NAME208',
            # '0x010000D0': 'rg.PAGE1_NAME209',
            # '0x010000D1': 'rg.PAGE1_NAME210',
            '0x010000D2': 'rg.PAGE1_NAME211',
            '0x010000D3': 'rg.PAGE1_NAME212',
            '0x010000D4': 'rg.PAGE1_NAME213',
            '0x010000D5': 'rg.PAGE1_NAME214',
            # '0x010000D6': 'rg.PAGE1_NAME215',
            # '0x010000D7': 'rg.PAGE1_NAME216',
            # '0x010000D8': 'rg.PAGE1_NAME217',
            # '0x010000D9': 'rg.PAGE1_NAME218',
            # '0x010000DA': 'rg.PAGE1_NAME219',
            # '0x010000DB': 'rg.PAGE1_NAME220',
            '0x010000DC': 'rg.PAGE1_NAME221',
            '0x010000DD': 'rg.PAGE1_NAME222',
            '0x010000DE': 'rg.PAGE1_NAME223',
            '0x010000DF': 'rg.PAGE1_NAME224',
            '0x010000E0': 'rg.PAGE1_NAME225',
            '0x010000E1': 'rg.PAGE1_NAME226',
            '0x010000E2': 'rg.PAGE1_NAME227',
            '0x010000E3': 'rg.PAGE1_NAME228',
            '0x010000E4': 'rg.PAGE1_NAME229',
            # '0x010000E5': 'rg.PAGE1_NAME230',
            # '0x010000E6': 'rg.PAGE1_NAME231',
            '0x010000E7': 'rg.PAGE1_NAME232',
            '0x010000E8': 'rg.PAGE1_NAME233',
            '0x010000E9': 'rg.PAGE1_NAME234',
            '0x010000EA': 'rg.PAGE1_NAME235',
            '0x010000EB': 'rg.PAGE1_NAME236',
            '0x010000EC': 'rg.PAGE1_NAME237',
            '0x010000ED': 'rg.PAGE1_NAME238',
            '0x010000EE': 'rg.PAGE1_NAME239',
            '0x010000EF': 'rg.PAGE1_NAME240',
            '0x010000F0': 'rg.PAGE1_NAME241',
            '0x010000F1': 'rg.PAGE1_NAME242',
            '0x010000F2': 'rg.PAGE1_NAME243',
            '0x010000F3': 'rg.PAGE1_NAME244',
            '0x010000F4': 'rg.PAGE1_NAME245',
            '0x010000F5': 'rg.PAGE1_NAME246',
            '0x010000F6': 'rg.PAGE1_NAME247',
            '0x010000F7': 'rg.PAGE1_NAME248',
            '0x010000F8': 'rg.PAGE1_NAME249',
            '0x010000F9': 'rg.PAGE1_NAME250',
            '0x010000FA': 'rg.PAGE1_NAME251',
            '0x010000FB': 'rg.PAGE1_NAME252',
            '0x010000FC': 'rg.PAGE1_NAME253',
            '0x010000FD': 'rg.PAGE1_NAME254',
            '0x010000FE': 'rg.PAGE1_NAME255',
            '0x010000FF': 'rg.PAGE1_NAME256',
            # page2
            '0x02000000': 'rg.PAGE2_NAME1',
            '0x02000001': 'rg.PAGE2_NAME2',
            '0x02000002': 'rg.PAGE2_NAME3',
            '0x02000003': 'rg.PAGE2_NAME4',
            '0x02000004': 'rg.PAGE2_NAME5',
            '0x02000005': 'rg.PAGE2_NAME6',
            '0x02000006': 'rg.PAGE2_NAME7',
            '0x02000007': 'rg.PAGE2_NAME8',
            '0x02000008': 'rg.PAGE2_NAME9',
            '0x02000009': 'rg.PAGE2_NAME10',
            '0x0200000A': 'rg.PAGE2_NAME11',
            '0x0200000B': 'rg.PAGE2_NAME12',
            '0x0200000C': 'rg.PAGE2_NAME13',
            '0x0200000D': 'rg.PAGE2_NAME14',
            '0x0200000E': 'rg.PAGE2_NAME15',
            '0x0200000F': 'rg.PAGE2_NAME16',
            '0x02000010': 'rg.PAGE2_NAME17',
            '0x02000011': 'rg.PAGE2_NAME18',
            '0x02000012': 'rg.PAGE2_NAME19',
            '0x02000013': 'rg.PAGE2_NAME20',
            '0x02000014': 'rg.PAGE2_NAME21',
            '0x02000015': 'rg.PAGE2_NAME22',
            '0x02000016': 'rg.PAGE2_NAME23',
            '0x02000017': 'rg.PAGE2_NAME24',
            '0x02000018': 'rg.PAGE2_NAME25',
            '0x02000019': 'rg.PAGE2_NAME26',
            '0x0200001A': 'rg.PAGE2_NAME27',
            '0x0200001B': 'rg.PAGE2_NAME28',
            '0x0200001C': 'rg.PAGE2_NAME29',
            '0x0200001D': 'rg.PAGE2_NAME30',
            '0x0200001E': 'rg.PAGE2_NAME31',
            '0x0200001F': 'rg.PAGE2_NAME32',
            '0x02000020': 'rg.PAGE2_NAME33',
            '0x02000021': 'rg.PAGE2_NAME34',
            '0x02000022': 'rg.PAGE2_NAME35',
            '0x02000023': 'rg.PAGE2_NAME36',
            '0x02000024': 'rg.PAGE2_NAME37',
            '0x02000025': 'rg.PAGE2_NAME38',
            '0x02000026': 'rg.PAGE2_NAME39',
            '0x02000027': 'rg.PAGE2_NAME40',
            '0x02000028': 'rg.PAGE2_NAME41',
            '0x02000029': 'rg.PAGE2_NAME42',
            '0x0200002A': 'rg.PAGE2_NAME43',
            '0x0200002B': 'rg.PAGE2_NAME44',
            '0x0200002C': 'rg.PAGE2_NAME45',
            '0x0200002D': 'rg.PAGE2_NAME46',
            '0x0200002E': 'rg.PAGE2_NAME47',
            '0x0200002F': 'rg.PAGE2_NAME48',
            '0x02000030': 'rg.PAGE2_NAME49',
            '0x02000031': 'rg.PAGE2_NAME50',
            '0x02000032': 'rg.PAGE2_NAME51',
            '0x02000033': 'rg.PAGE2_NAME52',
            '0x02000034': 'rg.PAGE2_NAME53',
            '0x02000035': 'rg.PAGE2_NAME54',
            '0x02000036': 'rg.PAGE2_NAME55',
            '0x02000037': 'rg.PAGE2_NAME56',
            '0x02000038': 'rg.PAGE2_NAME57',
            '0x02000039': 'rg.PAGE2_NAME58',
            '0x0200003A': 'rg.PAGE2_NAME59',
            '0x0200003B': 'rg.PAGE2_NAME60',
            '0x0200003C': 'rg.PAGE2_NAME61',
            '0x0200003D': 'rg.PAGE2_NAME62',
            '0x0200003E': 'rg.PAGE2_NAME63',
            '0x0200003F': 'rg.PAGE2_NAME64',
            '0x02000040': 'rg.PAGE2_NAME65',
            '0x02000041': 'rg.PAGE2_NAME66',
            '0x02000042': 'rg.PAGE2_NAME67',
            '0x02000043': 'rg.PAGE2_NAME68',
            '0x02000044': 'rg.PAGE2_NAME69',
            '0x02000045': 'rg.PAGE2_NAME70',
            '0x02000046': 'rg.PAGE2_NAME71',
            '0x02000047': 'rg.PAGE2_NAME72',
            '0x02000048': 'rg.PAGE2_NAME73',
            '0x02000049': 'rg.PAGE2_NAME74',
            '0x0200004A': 'rg.PAGE2_NAME75',
            '0x0200004B': 'rg.PAGE2_NAME76',
            '0x0200004C': 'rg.PAGE2_NAME77',
            '0x0200004D': 'rg.PAGE2_NAME78',
            '0x0200004E': 'rg.PAGE2_NAME79',
            '0x0200004F': 'rg.PAGE2_NAME80',
            '0x02000050': 'rg.PAGE2_NAME81',
            '0x02000051': 'rg.PAGE2_NAME82',
            '0x02000052': 'rg.PAGE2_NAME83',
            '0x02000053': 'rg.PAGE2_NAME84',
            '0x02000054': 'rg.PAGE2_NAME85',
            '0x02000055': 'rg.PAGE2_NAME86',
            '0x02000056': 'rg.PAGE2_NAME87',
            '0x02000057': 'rg.PAGE2_NAME88',
            '0x02000058': 'rg.PAGE2_NAME89',
            '0x02000059': 'rg.PAGE2_NAME90',
            '0x0200005A': 'rg.PAGE2_NAME91',
            '0x0200005B': 'rg.PAGE2_NAME92',
            '0x0200005C': 'rg.PAGE2_NAME93',
            '0x0200005D': 'rg.PAGE2_NAME94',
            '0x0200005E': 'rg.PAGE2_NAME95',
            '0x0200005F': 'rg.PAGE2_NAME96',
            '0x02000060': 'rg.PAGE2_NAME97',
            '0x02000061': 'rg.PAGE2_NAME98',
            '0x02000062': 'rg.PAGE2_NAME99',
            '0x02000063': 'rg.PAGE2_NAME100',
            '0x02000064': 'rg.PAGE2_NAME101',
            '0x02000065': 'rg.PAGE2_NAME102',
            '0x02000066': 'rg.PAGE2_NAME103',
            '0x02000067': 'rg.PAGE2_NAME104',
            '0x02000068': 'rg.PAGE2_NAME105',
            '0x02000069': 'rg.PAGE2_NAME106',
            '0x0200006A': 'rg.PAGE2_NAME107',
            '0x0200006B': 'rg.PAGE2_NAME108',
            '0x0200006C': 'rg.PAGE2_NAME109',
            '0x0200006D': 'rg.PAGE2_NAME110',
            '0x0200006E': 'rg.PAGE2_NAME111',
            '0x0200006F': 'rg.PAGE2_NAME112',
            '0x02000070': 'rg.PAGE2_NAME113',
            '0x02000071': 'rg.PAGE2_NAME114',
            '0x02000072': 'rg.PAGE2_NAME115',
            '0x02000073': 'rg.PAGE2_NAME116',
            '0x02000074': 'rg.PAGE2_NAME117',
            '0x02000075': 'rg.PAGE2_NAME118',
            '0x02000076': 'rg.PAGE2_NAME119',
            '0x02000077': 'rg.PAGE2_NAME120',
            '0x02000078': 'rg.PAGE2_NAME121',
            '0x02000079': 'rg.PAGE2_NAME122',
            '0x0200007A': 'rg.PAGE2_NAME123',
            '0x0200007B': 'rg.PAGE2_NAME124',
            '0x0200007C': 'rg.PAGE2_NAME125',
            '0x0200007D': 'rg.PAGE2_NAME126',
            '0x0200007E': 'rg.PAGE2_NAME127',
            '0x0200007F': 'rg.PAGE2_NAME128',
            '0x02000080': 'rg.PAGE2_NAME129',
            '0x02000081': 'rg.PAGE2_NAME130',
            '0x02000082': 'rg.PAGE2_NAME131',
            '0x02000083': 'rg.PAGE2_NAME132',
            '0x02000084': 'rg.PAGE2_NAME133',
            '0x02000085': 'rg.PAGE2_NAME134',
            '0x02000086': 'rg.PAGE2_NAME135',
            '0x02000087': 'rg.PAGE2_NAME136',
            '0x02000088': 'rg.PAGE2_NAME137',
            '0x02000089': 'rg.PAGE2_NAME138',
            '0x0200008A': 'rg.PAGE2_NAME139',
            '0x0200008B': 'rg.PAGE2_NAME140',
            '0x0200008C': 'rg.PAGE2_NAME141',
            '0x0200008D': 'rg.PAGE2_NAME142',
            '0x0200008E': 'rg.PAGE2_NAME143',
            '0x0200008F': 'rg.PAGE2_NAME144',
            '0x02000090': 'rg.PAGE2_NAME145',
            '0x02000091': 'rg.PAGE2_NAME146',
            '0x02000092': 'rg.PAGE2_NAME147',
            '0x02000093': 'rg.PAGE2_NAME148',
            '0x02000094': 'rg.PAGE2_NAME149',
            '0x02000095': 'rg.PAGE2_NAME150',
            '0x02000096': 'rg.PAGE2_NAME151',
            '0x02000097': 'rg.PAGE2_NAME152',
            '0x02000098': 'rg.PAGE2_NAME153',
            '0x02000099': 'rg.PAGE2_NAME154',
            '0x0200009A': 'rg.PAGE2_NAME155',
            '0x0200009B': 'rg.PAGE2_NAME156',
            '0x0200009C': 'rg.PAGE2_NAME157',
            '0x0200009D': 'rg.PAGE2_NAME158',
            '0x0200009E': 'rg.PAGE2_NAME159',
            '0x0200009F': 'rg.PAGE2_NAME160',
            # top_reg
            '0x03000000': 'rg.TOP_REG_NAME1',
            '0x03000001': 'rg.TOP_REG_NAME2',
            '0x03000002': 'rg.TOP_REG_NAME3',
            '0x03000003': 'rg.TOP_REG_NAME4',
            '0x03000004': 'rg.TOP_REG_NAME5',
            '0x03000005': 'rg.TOP_REG_NAME6',
            '0x03000006': 'rg.TOP_REG_NAME7',
            '0x03000007': 'rg.TOP_REG_NAME8',
            # gpio控制
            '0x03000008': 'rg.TOP_REG_NAME9',
            '0x03000009': 'rg.TOP_REG_NAME10',
            '0x0300000F': 'rg.TOP_REG_NAME16',
            # 新增控制
            '0x030000F0': 'rg.TOP_REG_NAME240',
            '0x030000F4': 'rg.TOP_REG_NAME244',
            '0x030000F8': 'rg.TOP_REG_NAME248',
            '0x030000FC': 'rg.TOP_REG_NAME252',

            }
TOP_REG_NAME252 = ["rxdc_Q[0]","rxdc_Q[1]","rxdc_Q[2]","rxdc_Q[3]","rxdc_Q[4]","rxdc_Q[4]","rxdc_Q[4]","rxdc_Q[7]",'0x00']
TOP_REG_NAME248 = ["rxdc_Q[8]","reserve","reserve","reserve","reserve","reserve","reserve","reserve",'0x00']
TOP_REG_NAME244 = ["rxdc_I[0]","rxdc_I[1]","rxdc_I[2]","rxdc_I[3]","rxdc_I[4]","rxdc_I[5]","rxdc_I[6]","rxdc_I[7]",'0x00']
TOP_REG_NAME240 = ["rxdc_I[8]","reserve","reserve","reserve","reserve","reserve","reserve","reserve",'0x00']
TOP_REG_NAME16 = ["EN_DIGLD0","AGC_IDX[0]","AGC_IDX[1]","AGC_IDX[2]","RXON","TXON","保留位","保留位","0X00"]
TOP_REG_NAME10 = ["XO fine tune[0]","XO fine tune[1]","XO fine tune[2]","XO fine tune[3]","XO fine tune[4]","XO fine tune[5]","XO fine tune[6]","XO fine tune[7]","0X00"]
TOP_REG_NAME9 = ["XO coarse tune[0]","XO coarse tune[1]","XO coarse tune[2]","XO coarse tune[3]","XO coarse tune[4]","XO coarse tune[5]","XO coarse tune[6]","保留位","0X00"]
TOP_REG_NAME8 = ["Name:pinmux_func1[0]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : txdac_in_q[8]\n1‘b1 : tmux_digout", "Name:pinmux_func1[1]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : agc_gainidx[1]\n1‘b1 : rxfltrtnr_vouti", "Name:pinmux_func1[2]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : agc_gainidx[0]\n1‘b1 : rxfltrtnr_voutq", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n保留", '0x00']
TOP_REG_NAME7 = ["Name:digldo_atst_en\nAccess: RW\nReset Value: 1'b0\nDescription:\ndig ldo 测试使能", "Name:digldo_atst_sel\nAccess: RW\nReset Value: 2'h0\nDescription:\ndig ldo 测试项选择", "Name:digldo_atst_sel\nAccess: RW\nReset Value: 2'h0\nDescription:\ndig ldo 测试项选择", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n保留", "Name:digldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\ndig ldo trim信号", "Name:digldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\ndig ldo trim信号", "Name:digldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\ndig ldo trim信号", "Name:digldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\ndig ldo trim信号", '0x00']
TOP_REG_NAME6 = ["Name:pad_adc_ds\nAccess: RW\nReset Value: 1'b0\nDescription:\npad_adc的驱动强度调节", "Name:pad_clk24m_clk_ds\nAccess: RW\nReset Value: 1'b0\nDescription:\npad_clk24m_clk的驱动强度调节", "Name:pad_spi_miso_ds\nAccess: RW\nReset Value: 1'b0\nDescription:\npad_spi_miso的驱动强度调节", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n保留", "Name:xoclk24m_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\n晶振时钟选择\n1'b0 : 使用内部24MHz晶振时钟\n1'b1 : 使用外部24MHz晶振时钟", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n保留", '0x00']
TOP_REG_NAME5 = ["Name:digctrl_tmux_digsel[3:0]\nAccess: RW\nReset Value: 4'b0\nDescription:\n数字测试信号选择", "Name:digctrl_tmux_digsel[3:0]\nAccess: RW\nReset Value: 4'b0\nDescription:\n数字测试信号选择", "Name:digctrl_tmux_digsel[3:0]\nAccess: RW\nReset Value: 4'b0\nDescription:\n数字测试信号选择", "Name:digctrl_tmux_digsel[3:0]\nAccess: RW\nReset Value: 4'b0\nDescription:\n数字测试信号选择", "Name:digctrl_tmux_digen\nAccess: RW\nReset Value: 1'b0\nDescription:\n数字测试信号输出总使能", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n保留", '0x00']
TOP_REG_NAME4 = ["Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", '0x00']
TOP_REG_NAME3 = ["Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", "Name:digctrl_digin_en[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n数字测试信号输出使能", '0x00']
TOP_REG_NAME2 = ["Name:pinmux_func[0]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : rxadc_out_i[0]\n1‘b1 : rxdata[0]", "Name:pinmux_func[1]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : rxadc_out_i[1]\n1‘b1 : rxdata[1]", "Name:pinmux_func[2]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : rxadc_out_i[2]\n1‘b1 : rxdata[2]", "Name:pinmux_func[3]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : rxadc_out_i[3]\n1‘b1 : rxvalid", "Name:pinmux_func[4]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : txdac_in_i[0]\n1‘b1 : txdata[0]", "Name:pinmux_func[5]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : txdac_in_i[1]\n1‘b1 : txdata[1]", "Name:pinmux_func[6]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : txdac_in_i[2]\n1‘b1 : txdata[2]", "Name:pinmux_func[7]\nAccess: RW\nReset Value: 1'b0\nDescription:\npad pinmux功能选择：\n1‘b0 : txdac_in_i[3]\n1‘b1 : txvalid", '0x00']
TOP_REG_NAME1 = ["Name:soft_rstn\nAccess: RW\nReset Value: 1'b1\nDescription:\n系统软件复位", "Name:digclken\nAccess: RW\nReset Value: 1'b1\nDescription:\n数字模块时钟使能信号，高有效", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n保留", "Name:stbyen\nAccess: RW\nReset Value: 1'b1\nDescription:\nStandby mode enalbe control. Used to operate the IP in the standby mode", "Name:oscen\nAccess: RW\nReset Value: 1'b1\nDescription:\n晶振使能控制信号，用于使能晶振电路", "Name:lvlshpd\nAccess: RW\nReset Value: 1'b0\nDescription:\nLevel shifters and CDM protection power down control. Used to disable the level shifters and CDM protection on the hard IP", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n保留", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n保留", '0x00']

PAGE2_NAME160 = ["Name:dc_avg_en\nAccess: RW\nReset Value: 1'b0\nDescription:\ndemodulator中average功能使能\n1’b1：对输入信号累加求平均\n1‘b0：bypass，输入直通到输出", "Name:dc_avg_mode\nAccess: RW\nReset Value: 2'b1\nDescription:\ndemodulator中average模式\n2’b00：4个数累加平均\n2‘b01：8个数累加平均\n2’b10：16个数累加平均\n2‘b11：32个数累加平均", "Name:dc_avg_mode\nAccess: RW\nReset Value: 2'b1\nDescription:\ndemodulator中average模式\n2’b00：4个数累加平均\n2‘b01：8个数累加平均\n2’b10：16个数累加平均\n2‘b11：32个数累加平均", "Name:dcofst_adc_op_avg_en\nAccess: RW\nReset Value: 1'b1\nDescription:\ndc offset average功能使能\n1’b1：对输入信号累加求平均\n1‘b0：bypass，输入直通到输出", "Name:dcofst_adc_op_avg_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\ndc offset average模式\n2’b00：4个数累加平均\n2‘b01：8个数累加平均\n2’b10：16个数累加平均\n2‘b11：32个数累加平均", "Name:dcofst_adc_op_avg_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\ndc offset average模式\n2’b00：4个数累加平均\n2‘b01：8个数累加平均\n2’b10：16个数累加平均\n2‘b11：32个数累加平均", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE2_NAME159 = ["Name:dcofst_single_region_index\nAccess: RW\nReset Value: 3'h0\nDescription:\n配置一组模拟gain，对这组gain进行单纯dc offset校准", "Name:dcofst_single_region_index\nAccess: RW\nReset Value: 3'h0\nDescription:\n配置一组模拟gain，对这组gain进行单纯dc offset校准", "Name:dcofst_single_region_index\nAccess: RW\nReset Value: 3'h0\nDescription:\n配置一组模拟gain，对这组gain进行单纯dc offset校准", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME158 = ["Name:dcofst_resid_cal_done\nAccess: RO\nReset Value: 1'b0\nDescription:\n完成残余直流校准的结束标志", "Name:dcofst_multi_region_bin_srch_done\nAccess: RO\nReset Value: 1'b0\nDescription:\n以bin_srch完成直流校准的结束标志", "Name:dcofst_multi_region_loop_done\nAccess: RO\nReset Value: 1'b0\nDescription:\n以integrator完成直流校准的结束标志", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME157 = ["Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", '0x00']
PAGE2_NAME156 = ["Name:dcofst_quad_residual_gain7[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain7[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain7的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME155 = ["Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", '0x00']
PAGE2_NAME154 = ["Name:dcofst_quad_residual_gain6[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain6[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain6的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME153 = ["Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", '0x00']
PAGE2_NAME152 = ["Name:dcofst_quad_residual_gain5[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain5[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain5的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME151 = ["Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", '0x00']
PAGE2_NAME150 = ["Name:dcofst_quad_residual_gain4[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain4[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain4的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME149 = ["Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", '0x00']
PAGE2_NAME148 = ["Name:dcofst_quad_residual_gain3[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain3[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain3的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME147 = ["Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", '0x00']
PAGE2_NAME146 = ["Name:dcofst_quad_residual_gain2[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain2[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain2的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME145 = ["Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", '0x00']
PAGE2_NAME144 = ["Name:dcofst_quad_residual_gain1[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain1[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain1的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME143 = ["Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", '0x00']
PAGE2_NAME142 = ["Name:dcofst_quad_residual_gain0[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:dcofst_quad_residual_gain0[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain0的Q路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME141 = ["Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路残余直流校准的结果", '0x00']
PAGE2_NAME140 = ["Name:dcofst_inph_residual_gain7[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain7[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain7的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME139 = ["Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路残余直流校准的结果", '0x00']
PAGE2_NAME138 = ["Name:dcofst_inph_residual_gain6[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain6[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain6的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME137 = ["Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路残余直流校准的结果", '0x00']
PAGE2_NAME136 = ["Name:dcofst_inph_residual_gain5[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain5[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain5的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME135 = ["Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路残余直流校准的结果", '0x00']
PAGE2_NAME134 = ["Name:dcofst_inph_residual_gain4[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain4[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain4的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME133 = ["Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路残余直流校准的结果", '0x00']
PAGE2_NAME132 = ["Name:dcofst_inph_residual_gain3[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain3[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain3的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME131 = ["Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路残余直流校准的结果", '0x00']
PAGE2_NAME130 = ["Name:dcofst_inph_residual_gain2[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain2[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain2的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME129 = ["Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路残余直流校准的结果", '0x00']
PAGE2_NAME128 = ["Name:dcofst_inph_residual_gain1[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain1[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain1的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME127 = ["Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路残余直流校准的结果", '0x00']
PAGE2_NAME126 = ["Name:dcofst_inph_residual_gain0[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:dcofst_inph_residual_gain0[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\n针对gain0的I路残余直流校准的结果", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME125 = ["Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的Q路直流校准结果", '0x00']
PAGE2_NAME124 = ["Name:dcofst_quad_dc_word_gain7[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain7的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME123 = ["Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的Q路直流校准结果", '0x00']
PAGE2_NAME122 = ["Name:dcofst_quad_dc_word_gain6[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain6的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME121 = ["Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的Q路直流校准结果", '0x00']
PAGE2_NAME120 = ["Name:dcofst_quad_dc_word_gain5[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain5的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME119 = ["Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的Q路直流校准结果", '0x00']
PAGE2_NAME118 = ["Name:dcofst_quad_dc_word_gain4[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain4的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME117 = ["Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的Q路直流校准结果", '0x00']
PAGE2_NAME116 = ["Name:dcofst_quad_dc_word_gain3[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain3的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME115 = ["Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的Q路直流校准结果", '0x00']
PAGE2_NAME114 = ["Name:dcofst_quad_dc_word_gain2[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain2的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME113 = ["Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的Q路直流校准结果", '0x00']
PAGE2_NAME112 = ["Name:dcofst_quad_dc_word_gain1[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain1的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME111 = ["Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:dcofst_quad_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的Q路直流校准结果", '0x00']
PAGE2_NAME110 = ["Name:dcofst_quad_dc_word_gain0[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain0的Q路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME109 = ["Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain7[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain7的I路直流校准结果", '0x00']
PAGE2_NAME108 = ["Name:dcofst_inphase_dc_word_gain7[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain7的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME107 = ["Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain6[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain6的I路直流校准结果", '0x00']
PAGE2_NAME106 = ["Name:dcofst_inphase_dc_word_gain6[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain6的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME105 = ["Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain5[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain5的I路直流校准结果", '0x00']
PAGE2_NAME104 = ["Name:dcofst_inphase_dc_word_gain5[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain5的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME103 = ["Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain4[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain4的I路直流校准结果", '0x00']
PAGE2_NAME102 = ["Name:dcofst_inphase_dc_word_gain4[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain4的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME101 = ["Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain3[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain3的I路直流校准结果", '0x00']
PAGE2_NAME100 = ["Name:dcofst_inphase_dc_word_gain3[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain3的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME99 = ["Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain2[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain2的I路直流校准结果", '0x00']
PAGE2_NAME98 = ["Name:dcofst_inphase_dc_word_gain2[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain2的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME97 = ["Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain1[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain1的I路直流校准结果", '0x00']
PAGE2_NAME96 = ["Name:dcofst_inphase_dc_word_gain1[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain1的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME95 = ["Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:dcofst_inphase_dc_word_gain0[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n针对gain0的I路直流校准结果", '0x00']
PAGE2_NAME94 = ["Name:dcofst_inphase_dc_word_gain0[8]\nAccess: RO\nReset Value: 1'b0\nDescription:\n针对gain0的I路直流校准结果", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME93 = ["Name:demod_wide_bw_a2\nAccess: RW\nReset Value: 6'h7\nDescription:\n", "Name:demod_wide_bw_a2\nAccess: RW\nReset Value: 6'h7\nDescription:\n", "Name:demod_wide_bw_a2\nAccess: RW\nReset Value: 6'h7\nDescription:\n", "Name:demod_wide_bw_a2\nAccess: RW\nReset Value: 6'h7\nDescription:\n", "Name:demod_wide_bw_a2\nAccess: RW\nReset Value: 6'h7\nDescription:\n", "Name:demod_wide_bw_a2\nAccess: RW\nReset Value: 6'h7\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE2_NAME92 = ["Name:demod_wide_bw_a1\nAccess: RW\nReset Value: 6'h2E\nDescription:\n", "Name:demod_wide_bw_a1\nAccess: RW\nReset Value: 6'h2E\nDescription:\n", "Name:demod_wide_bw_a1\nAccess: RW\nReset Value: 6'h2E\nDescription:\n", "Name:demod_wide_bw_a1\nAccess: RW\nReset Value: 6'h2E\nDescription:\n", "Name:demod_wide_bw_a1\nAccess: RW\nReset Value: 6'h2E\nDescription:\n", "Name:demod_wide_bw_a1\nAccess: RW\nReset Value: 6'h2E\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE2_NAME91 = ["Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[7:0]\nAccess: RW\nReset Value: 3'hD4\nDescription:\n解调模块notch filter输入相乘的系数", '0x00']
PAGE2_NAME90 = ["Name:demod_notch_gain[10:8]\nAccess: RW\nReset Value: 3'h6\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[10:8]\nAccess: RW\nReset Value: 3'h6\nDescription:\n解调模块notch filter输入相乘的系数", "Name:demod_notch_gain[10:8]\nAccess: RW\nReset Value: 3'h6\nDescription:\n解调模块notch filter输入相乘的系数", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME89 = ["Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[7:0]\nAccess: RW\nReset Value: 3'h6A\nDescription:\n解调模块notch filter输出反馈的系数", '0x00']
PAGE2_NAME88 = ["Name:demod_notch_coeff[10:8]\nAccess: RW\nReset Value: 3'h7\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[10:8]\nAccess: RW\nReset Value: 3'h7\nDescription:\n解调模块notch filter输出反馈的系数", "Name:demod_notch_coeff[10:8]\nAccess: RW\nReset Value: 3'h7\nDescription:\n解调模块notch filter输出反馈的系数", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME87 = ["Name:demod_notch_init\nAccess: RW\nReset Value: 1'b1\nDescription:\n解调模拟motch filter使能初始状态\n1’b1：notch filter启动时，先经过初始状态\n1‘b0：notch filter启动时，无初始状态", "Name:denod_subtract_ofst\nAccess: RW\nReset Value: 1'b0\nDescription:\n解调模拟消去残余直流的使能\n1’b1：ADC后的IQ数据，减去残余直流后再进行解调\n1‘b0：ADC后的IQ数据直接解调", "Name:demod_enable_notch\nAccess: RW\nReset Value: 1'b0\nDescription:\n解调模拟notch filter使能", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME86 = ["Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", '0x00']
PAGE2_NAME85 = ["Name:dcofst_quad_residual_gain7_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:dcofst_quad_residual_gain7_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain7的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME84 = ["Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", '0x00']
PAGE2_NAME83 = ["Name:dcofst_quad_residual_gain6_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:dcofst_quad_residual_gain6_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain6的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME82 = ["Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", '0x00']
PAGE2_NAME81 = ["Name:dcofst_quad_residual_gain5_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:dcofst_quad_residual_gain5_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain5的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME80 = ["Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", '0x00']
PAGE2_NAME79 = ["Name:dcofst_quad_residual_gain4_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:dcofst_quad_residual_gain4_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain4的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME78 = ["Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", '0x00']
PAGE2_NAME77 = ["Name:dcofst_quad_residual_gain3_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:dcofst_quad_residual_gain3_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain3的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME76 = ["Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", '0x00']
PAGE2_NAME75 = ["Name:dcofst_quad_residual_gain2_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:dcofst_quad_residual_gain2_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain2的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME74 = ["Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", '0x00']
PAGE2_NAME73 = ["Name:dcofst_quad_residual_gain1_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:dcofst_quad_residual_gain1_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain1的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME72 = ["Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", '0x00']
PAGE2_NAME71 = ["Name:dcofst_quad_residual_gain0_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:dcofst_quad_residual_gain0_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置Q路gain0的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME70 = ["Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", '0x00']
PAGE2_NAME69 = ["Name:dcofst_inph_residual_gain7_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:dcofst_inph_residual_gain7_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain7的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME68 = ["Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", '0x00']
PAGE2_NAME67 = ["Name:dcofst_inph_residual_gain6_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:dcofst_inph_residual_gain6_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain6的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME66 = ["Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", '0x00']
PAGE2_NAME65 = ["Name:dcofst_inph_residual_gain5_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:dcofst_inph_residual_gain5_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain5的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME64 = ["Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", '0x00']
PAGE2_NAME63 = ["Name:dcofst_inph_residual_gain4_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:dcofst_inph_residual_gain4_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain4的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME62 = ["Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", '0x00']
PAGE2_NAME61 = ["Name:dcofst_inph_residual_gain3_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:dcofst_inph_residual_gain3_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain3的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME60 = ["Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", '0x00']
PAGE2_NAME59 = ["Name:dcofst_inph_residual_gain2_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:dcofst_inph_residual_gain2_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain2的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME58 = ["Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", '0x00']
PAGE2_NAME57 = ["Name:dcofst_inph_residual_gain1_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:dcofst_inph_residual_gain1_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain1的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME56 = ["Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", '0x00']
PAGE2_NAME55 = ["Name:dcofst_inph_residual_gain0_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:dcofst_inph_residual_gain0_ovrd[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\n寄存器强制配置I路gain0的残余直流数值", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE2_NAME54 = ["Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:dcofst_quad_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", '0x00']
PAGE2_NAME53 = ["Name:dcofst_quad_dc_word_gain7_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain7的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME52 = ["Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:dcofst_quad_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", '0x00']
PAGE2_NAME51 = ["Name:dcofst_quad_dc_word_gain6_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain6的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME50 = ["Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:dcofst_quad_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", '0x00']
PAGE2_NAME49 = ["Name:dcofst_quad_dc_word_gain5_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain5的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME48 = ["Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:dcofst_quad_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", '0x00']
PAGE2_NAME47 = ["Name:dcofst_quad_dc_word_gain4_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain4的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME46 = ["Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:dcofst_quad_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", '0x00']
PAGE2_NAME45 = ["Name:dcofst_quad_dc_word_gain3_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain3的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME44 = ["Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:dcofst_quad_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", '0x00']
PAGE2_NAME43 = ["Name:dcofst_quad_dc_word_gain2_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain2的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME42 = ["Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:dcofst_quad_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", '0x00']
PAGE2_NAME41 = ["Name:dcofst_quad_dc_word_gain1_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain1的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME40 = ["Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:dcofst_quad_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", '0x00']
PAGE2_NAME39 = ["Name:dcofst_quad_dc_word_gain0_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对Q路gain0的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME38 = ["Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:dcofst_inphase_dc_word_gain7_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", '0x00']
PAGE2_NAME37 = ["Name:dcofst_inphase_dc_word_gain7_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain7的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME36 = ["Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:dcofst_inphase_dc_word_gain6_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", '0x00']
PAGE2_NAME35 = ["Name:dcofst_inphase_dc_word_gain6_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain6的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME34 = ["Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:dcofst_inphase_dc_word_gain5_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", '0x00']
PAGE2_NAME33 = ["Name:dcofst_inphase_dc_word_gain5_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain5的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME32 = ["Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:dcofst_inphase_dc_word_gain4_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", '0x00']
PAGE2_NAME31 = ["Name:dcofst_inphase_dc_word_gain4_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain4的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME30 = ["Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:dcofst_inphase_dc_word_gain3_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", '0x00']
PAGE2_NAME29 = ["Name:dcofst_inphase_dc_word_gain3_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain3的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME28 = ["Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:dcofst_inphase_dc_word_gain2_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", '0x00']
PAGE2_NAME27 = ["Name:dcofst_inphase_dc_word_gain2_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain2的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME26 = ["Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:dcofst_inphase_dc_word_gain1_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", '0x00']
PAGE2_NAME25 = ["Name:dcofst_inphase_dc_word_gain1_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain1的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME24 = ["Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:dcofst_inphase_dc_word_gain0_ovrd[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", '0x00']
PAGE2_NAME23 = ["Name:dcofst_inphase_dc_word_gain0_ovrd[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置对I路gain0的直流数值", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME22 = ["Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[7:0]\nAccess: RW\nReset Value: 8'hD7\nDescription:\n直流校准积分器系数", '0x00']
PAGE2_NAME21 = ["Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:dcofst_integrator_const[14:8]\nAccess: RW\nReset Value: 7'h7F\nDescription:\n直流校准积分器系数", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE2_NAME20 = ["Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:dcofst_integ_debnc_thresh_q[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器Q路的判决门限", '0x00']
PAGE2_NAME19 = ["Name:dcofst_integ_debnc_thresh_q[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n直流柜校准积分器Q路的判决门限", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE2_NAME18 = ["Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integ_debnc_thresh_i[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n直流柜校准积分器I路的判决门限", '0x00']
PAGE2_NAME17 = ["Name:dcofst_integ_debnc_thresh_i[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\n直流柜校准积分器I路的判决门限", "Name:dcofst_integrator_load_op\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器配置直流校准积分器的重装载", "Name:dcofst_integrator_load_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置校准结果的使能", "Name:dcofst_integrator_enable\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器配置直流校准积分器的使能", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE2_NAME16 = ["Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k4[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", '0x00']
PAGE2_NAME15 = ["Name:dcofst_ph_shft_k4[10:8]\nAccess: RW\nReset Value: 3'h5\nDescription:\n", "Name:dcofst_ph_shft_k4[10:8]\nAccess: RW\nReset Value: 3'h5\nDescription:\n", "Name:dcofst_ph_shft_k4[10:8]\nAccess: RW\nReset Value: 3'h5\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME14 = ["Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", "Name:dcofst_ph_shft_k3[7:0]\nAccess: RW\nReset Value: 8'h9C\nDescription:\n", '0x00']
PAGE2_NAME13 = ["Name:dcofst_ph_shft_k3[10:8]\nAccess: RW\nReset Value: 3'h4\nDescription:\n", "Name:dcofst_ph_shft_k3[10:8]\nAccess: RW\nReset Value: 3'h4\nDescription:\n", "Name:dcofst_ph_shft_k3[10:8]\nAccess: RW\nReset Value: 3'h4\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME12 = ["Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", "Name:dcofst_ph_shft_k2[7:0]\nAccess: RW\nReset Value: 8'h64\nDescription:\n", '0x00']
PAGE2_NAME11 = ["Name:dcofst_ph_shft_k2[10:8]\nAccess: RW\nReset Value: 3'h3\nDescription:\n", "Name:dcofst_ph_shft_k2[10:8]\nAccess: RW\nReset Value: 3'h3\nDescription:\n", "Name:dcofst_ph_shft_k2[10:8]\nAccess: RW\nReset Value: 3'h3\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME10 = ["Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", "Name:dcofst_ph_shft_k1[7:0]\nAccess: RW\nReset Value: 8'hE2\nDescription:\n", '0x00']
PAGE2_NAME9 = ["Name:dcofst_ph_shft_k1[10:8]\nAccess: RW\nReset Value: 3'h5\nDescription:\n", "Name:dcofst_ph_shft_k1[10:8]\nAccess: RW\nReset Value: 3'h5\nDescription:\n", "Name:dcofst_ph_shft_k1[10:8]\nAccess: RW\nReset Value: 3'h5\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE2_NAME8 = ["Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[7:0]\nAccess: RW\nReset Value: 8'h50\nDescription:\n对每个gain进行残余直流校准的时长配置", '0x00']
PAGE2_NAME7 = ["Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:dcofst_dc_resid_wait_time[14:8]\nAccess: RW\nReset Value: 7'b0\nDescription:\n对每个gain进行残余直流校准的时长配置", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE2_NAME6 = ["Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[7:0]\nAccess: RW\nReset Value: 8'h76\nDescription:\n以integrator进行直流校准的总时长配置", '0x00']
PAGE2_NAME5 = ["Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[15:8]\nAccess: RW\nReset Value: 8'h8\nDescription:\n以integrator进行直流校准的总时长配置", '0x00']
PAGE2_NAME4 = ["Name:dcofst_loop_calib_wait_time[17:16]\nAccess: RW\nReset Value: 8'h0\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_loop_calib_wait_time[17:16]\nAccess: RW\nReset Value: 8'h0\nDescription:\n以integrator进行直流校准的总时长配置", "Name:dcofst_clk_sel_32\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx dc ofset cal时钟选择\n1’b1：32MHz晶振时钟\n1‘b0：16MHz时钟，由32MHz晶振分频而成 ", "Name:dcofst_frc_gain_enable\nAccess: RW\nReset Value: 1'b1\nDescription:\ndc offset校准时，强制锁定模拟gain的使能", "Name:dcofst_drift_mode\nAccess: RW\nReset Value: 1'b0\nDescription:\nbin_srch偏置模式，不以理论的0作为起始条件去校准，以上一次校准的结果作为起始条件", "Name:dcofst_drift_iter_cfg\nAccess: RW\nReset Value: 2'h5\nDescription:\nbin_srch偏置模式的档位\n选择起始条件的取值范围以及迭代次数", "Name:dcofst_drift_iter_cfg\nAccess: RW\nReset Value: 2'h5\nDescription:\nbin_srch偏置模式的档位\n选择起始条件的取值范围以及迭代次数", "Name:dcofst_drift_iter_cfg\nAccess: RW\nReset Value: 2'h5\nDescription:\nbin_srch偏置模式的档位\n选择起始条件的取值范围以及迭代次数", '0x00']
PAGE2_NAME3 = ["Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", "Name:dcofst_gain_wait_time\nAccess: RW\nReset Value: 8'h30\nDescription:\n针对不同模拟增益进行的直流校准，每个模拟增益下的校准时长配置", '0x00']
PAGE2_NAME2 = ["Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", "Name:dcofst_dac_wait_time\nAccess: RW\nReset Value: 8'h40\nDescription:\nbin_srch进行时，每次改变dac输出后，留给反馈回路的响应时间", '0x00']
PAGE2_NAME1 = ["Name:dcofst_captr_fltrd_resid\nAccess: RW\nReset Value: 1'b1\nDescription:\n残余直流校准的模式选择\n1’b1：校准结果采用notch filter输出的直流值\n1‘b0：校准结果采用dcoc中avg模块的输出值", "Name:dcofst_dc_residual_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置残余直流校准结果的使能", "Name:dcofst_dc_coarse_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n寄存器强制配置送给DAC的直流校准结果的使能", "Name:dcofst_start_residual_cal\nAccess: RW\nReset Value: 1'b0\nDescription:\n残余直流校准使能(结果给数字去消除直流)", "Name:dcofst_cal_mode_bin_srch\nAccess: RW\nReset Value: 1'b0\nDescription:\n直流校准的模式选择\n1‘b1：二分法bin_srch\n1'b0：积分器integrator", "Name:dcofst_tuner_mode\nAccess: RW\nReset Value: 1'b0\nDescription:\n1‘b1：启动rx filter tuner模式\n1’b0：dc offset模块正常工作模式", "Name:dcofst_signle_region\nAccess: RW\nReset Value: 1'b0\nDescription:\n针对单一模拟gain进行dc offset校准", "Name:dcofst_start_multi_region\nAccess: RW\nReset Value: 1'b0\nDescription:\n直流校准使能(结果给模拟去除直流)", '0x00']

PAGE1_NAME256 = ["Name:regmap_page_select\nAccess: RW\nReset Value: 1'b0\nDescription:\n用于选择寄存器page1或者page2\n1'b0: page1\n1'b1: page2", "Name:modem_bypass\nAccess: RW\nReset Value: 1'b1\nDescription:\nmodem输入信号bypass功能\n1'b0：使用I和Q信号\n1'b1：使用txdata和rxdata", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME255 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME254 = ["Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[7:0]\nAccess: RW\nReset Value: 8'h8\nDescription:\nthe time required to perform CCA detection in symbol periods", '0x00']
PAGE1_NAME253 = ["Name:cca_wait_time[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_wait_time[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nthe time required to perform CCA detection in symbol periods", "Name:cca_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\n2'b00: Mode1\n2'b01: Mode2\n2'b10: Mode3(ED AND CS)\n2'b11: Mode3(ED OR CS)", "Name:cca_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\n2'b00: Mode1\n2'b01: Mode2\n2'b10: Mode3(ED AND CS)\n2'b11: Mode3(ED OR CS)", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME252 = ["Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", "Name:sfd_word\nAccess: RW\nReset Value: 8'he5\nDescription:\nstart-of-frame delimiter setting", '0x00']
PAGE1_NAME251 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME250 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME249 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME248 = ["Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:rxfltr_preamble_word\nAccess: RW\nReset Value: 7'h22\nDescription:\nRX filter校准值", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME247 = ["Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:agc_rssi_delta_md\nAccess: RW\nReset Value: 1'b1\nDescription:\nrssi插值检测模式使能", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:rxfltr_preamble_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\nagc对filter tuner的旁路模式配置", "Name:rxfltr_preamble_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\nagc对filter tuner的旁路模式配置", '0x00']
PAGE1_NAME246 = ["Name:agc_ovrd_mixer\nAccess: RW\nReset Value: 2'b0\nDescription:\n当cntrl_force_gain（r0095 bit[3]）=1时，MIXER增益有寄存器控制", "Name:agc_ovrd_mixer\nAccess: RW\nReset Value: 2'b0\nDescription:\n当cntrl_force_gain（r0095 bit[3]）=1时，MIXER增益有寄存器控制", "Name:agc_ovrd_fltr\nAccess: RW\nReset Value: 1'b0\nDescription:\n当cntrl_force_gain（r0095 bit[3]）=1时，RX FILTER增益有寄存器控制", "Name:agc_ovrd_lna\nAccess: RW\nReset Value: 2'b0\nDescription:\n当cntrl_force_gain（r0095 bit[3]）=1时，LNA增益有寄存器控制", "Name:agc_ovrd_lna\nAccess: RW\nReset Value: 2'b0\nDescription:\n当cntrl_force_gain（r0095 bit[3]）=1时，LNA增益有寄存器控制", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME245 = ["Name:demod_iq_scale_th0\nAccess: RW\nReset Value: 4'h1\nDescription:\n", "Name:demod_iq_scale_th0\nAccess: RW\nReset Value: 4'h1\nDescription:\n", "Name:demod_iq_scale_th0\nAccess: RW\nReset Value: 4'h1\nDescription:\n", "Name:demod_iq_scale_th0\nAccess: RW\nReset Value: 4'h1\nDescription:\n", "Name:demod_bb_rdy_mode\nAccess: RW\nReset Value: 1'b1\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME244 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME243 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME242 = ["Name:iq_scale_value\nAccess: RW\nReset Value: 6'hd\nDescription:\n", "Name:iq_scale_value\nAccess: RW\nReset Value: 6'hd\nDescription:\n", "Name:iq_scale_value\nAccess: RW\nReset Value: 6'hd\nDescription:\n", "Name:iq_scale_value\nAccess: RW\nReset Value: 6'hd\nDescription:\n", "Name:iq_scale_value\nAccess: RW\nReset Value: 6'hd\nDescription:\n", "Name:iq_scale_value\nAccess: RW\nReset Value: 6'hd\nDescription:\n", "Name:iq_scl_acc_mtch_frz\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME241 = ["Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", "Name:gain_switch_timeout\nAccess: RW\nReset Value: 8'h5\nDescription:\n", '0x00']
PAGE1_NAME240 = ["Name:chk_cmp_err\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:symbol_chk_err\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:symbol_chk_err\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:symbol_chk_err\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:symbol_chk_err\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:symbol_chk_err\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:symbol_chk_err\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:symbol_err\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME239 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:pmubuckimaxcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckimaxcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckimaxcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckimaxcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckimaxcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME238 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:pmubuckrefcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckrefcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckrefcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckrefcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckrefcal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME237 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:pmubuckzercal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckzercal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckzercal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckzercal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:pmubuckzercal_tst\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME236 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:pmubuckimaxcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckimaxcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckimaxcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckimaxcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckimaxcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", '0x00']
PAGE1_NAME235 = ["Name:pmubuckrefcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckrefcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckrefcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckrefcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckrefcal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckcalen\nAccess: RW\nReset Value: 3'h7\nDescription:\n", "Name:pmubuckcalen\nAccess: RW\nReset Value: 3'h7\nDescription:\n", "Name:pmubuckcalen\nAccess: RW\nReset Value: 3'h7\nDescription:\n", '0x00']
PAGE1_NAME234 = ["Name:pmubuckzercal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckzercal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckzercal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckzercal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckzercal\nAccess: RW\nReset Value: 5'h10\nDescription:\n", "Name:pmubuckimax\nAccess: RW\nReset Value: 3'h4\nDescription:\n", "Name:pmubuckimax\nAccess: RW\nReset Value: 3'h4\nDescription:\n", "Name:pmubuckimax\nAccess: RW\nReset Value: 3'h4\nDescription:\n", '0x00']
PAGE1_NAME233 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME232 = ["Name:pmuptatvtrim\nAccess: RW\nReset Value: 3'h4\nDescription:\nPTAT Trimming 控制字", "Name:pmuptatvtrim\nAccess: RW\nReset Value: 3'h4\nDescription:\nPTAT Trimming 控制字", "Name:pmuptatvtrim\nAccess: RW\nReset Value: 3'h4\nDescription:\nPTAT Trimming 控制字", "Name:Reserved\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:Reserved\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:Reserved\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:Reserved\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:Reserved\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME231 = ["Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", "Name:soft_th2\nAccess: RW\nReset Value: 8'h46\nDescription:\n解调器相关的一个门限值", '0x00']
PAGE1_NAME230 = ["Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", "Name:soft_th1\nAccess: RW\nReset Value: 8'h14\nDescription:\n解调器相关的一个门限值", '0x00']
PAGE1_NAME229 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME228 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME227 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME226 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME225 = ["Name:cca_done\nAccess: RO\nReset Value: 1'b0\nDescription:\ncca done flag", "Name:cca_status\nAccess: RO\nReset Value: 1'b0\nDescription:\n1'b0: free channel\n1'b1: busy channel", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME224 = ["Name:ffe_thresh\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:ffe_thresh\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:ffe_thresh\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:ffe_thresh\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME223 = ["Name:unfilt_thresh\nAccess: RW\nReset Value: 4'h7\nDescription:\n", "Name:unfilt_thresh\nAccess: RW\nReset Value: 4'h7\nDescription:\n", "Name:unfilt_thresh\nAccess: RW\nReset Value: 4'h7\nDescription:\n", "Name:unfilt_thresh\nAccess: RW\nReset Value: 4'h7\nDescription:\n", "Name:coeff_unfilt\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:coeff_unfilt\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:coeff_unfilt\nAccess: RW\nReset Value: 4'h5\nDescription:\n", "Name:coeff_unfilt\nAccess: RW\nReset Value: 4'h5\nDescription:\n", '0x00']
PAGE1_NAME222 = ["Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", "Name:max_peak_trough_diff\nAccess: RW\nReset Value: 8'h96\nDescription:\n", '0x00']
PAGE1_NAME221 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:cca_en\nAccess: RW\nReset Value: 1'b0\nDescription:\n空闲信道评估模块（CCA）使能", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:ed_en\nAccess: RW\nReset Value: 1'b0\nDescription:\n功率评估（ED）使能", "Name:dig_cmplxfltr_en\nAccess: RW\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME220 = ["Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", "Name:preemph_coeff[7:0]\nAccess: RW\nReset Value: 8'h1b\nDescription:\n预加重系数", '0x00']
PAGE1_NAME219 = ["Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:preemph_coeff[14:8]\nAccess: RW\nReset Value: 7'h0\nDescription:\n预加重系数", "Name:NA\nAccess: RW\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME218 = ["Name:preemph_wre\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:nn\nAccess: RW\nReset Value: 5'b0\nDescription:\n", "Name:nn\nAccess: RW\nReset Value: 5'b0\nDescription:\n", "Name:nn\nAccess: RW\nReset Value: 5'b0\nDescription:\n", "Name:nn\nAccess: RW\nReset Value: 5'b0\nDescription:\n", "Name:nn\nAccess: RW\nReset Value: 5'b0\nDescription:\n", "Name:preemph_bypass\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME217 = ["Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\nReserved", 'Name:gauss_wre\nAccess: RW\nReset Value: 1‘b0\nDescription:\n高斯系数寄存器堆写入位。\n高斯系数表在模块里被存入一个24*12-bit的寄存器堆内，实际使用的时候通过查表读出，系数表通过寄存器写入。\n写入方法是：\n先配置好寄存器gauss_coeff[11:0]，再配置好地址gauss_index(0~23)，最后在gauss_wre位上写1后写0。', "Name:gauss_index[4:0]\nAccess: RW\nReset Value: 5'h0\nDescription:\n高斯系数寄存器堆写入地址", "Name:gauss_index[4:0]\nAccess: RW\nReset Value: 5'h0\nDescription:\n高斯系数寄存器堆写入地址", "Name:gauss_index[4:0]\nAccess: RW\nReset Value: 5'h0\nDescription:\n高斯系数寄存器堆写入地址", "Name:gauss_index[4:0]\nAccess: RW\nReset Value: 5'h0\nDescription:\n高斯系数寄存器堆写入地址", "Name:gauss_index[4:0]\nAccess: RW\nReset Value: 5'h0\nDescription:\n高斯系数寄存器堆写入地址", '0x00']
PAGE1_NAME216 = ["Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:gauss_coeff[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", '0x00']
PAGE1_NAME215 = ["Name:gauss_coeff[11:8]\nAccess: RW\nReset Value: 4'h0\nDescription:\n高斯系数寄存器堆写入数据", "Name:gauss_coeff[11:8]\nAccess: RW\nReset Value: 4'h0\nDescription:\n高斯系数寄存器堆写入数据", "Name:gauss_coeff[11:8]\nAccess: RW\nReset Value: 4'h0\nDescription:\n高斯系数寄存器堆写入数据", "Name:gauss_coeff[11:8]\nAccess: RW\nReset Value: 4'h0\nDescription:\n高斯系数寄存器堆写入数据", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\nReserved", '0x00']
PAGE1_NAME214 = ["Name:en_reduced_preamble\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:min_pk_tr_rdcd_pmble\nAccess: RW\nReset Value: 6'b0\nDescription:\n", "Name:min_pk_tr_rdcd_pmble\nAccess: RW\nReset Value: 6'b0\nDescription:\n", "Name:min_pk_tr_rdcd_pmble\nAccess: RW\nReset Value: 6'b0\nDescription:\n", "Name:min_pk_tr_rdcd_pmble\nAccess: RW\nReset Value: 6'b0\nDescription:\n", "Name:min_pk_tr_rdcd_pmble\nAccess: RW\nReset Value: 6'b0\nDescription:\n", "Name:min_pk_tr_rdcd_pmble\nAccess: RW\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME213 = ["Name:rssi_src[1:0]\nAccess: RW\nReset Value: 2'b0\nDescription:\n", "Name:rssi_src[1:0]\nAccess: RW\nReset Value: 2'b0\nDescription:\n", "Name:demod_notch_to_rssi\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:iq_adc_gain\nAccess: RW\nReset Value: 3'h1\nDescription:\n", "Name:iq_adc_gain\nAccess: RW\nReset Value: 3'h1\nDescription:\n", "Name:iq_adc_gain\nAccess: RW\nReset Value: 3'h1\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME212 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:dfe_ff_coeff[4:0]\nAccess: RW\nReset Value: 5'hf\nDescription:\n", "Name:dfe_ff_coeff[4:0]\nAccess: RW\nReset Value: 5'hf\nDescription:\n", "Name:dfe_ff_coeff[4:0]\nAccess: RW\nReset Value: 5'hf\nDescription:\n", "Name:dfe_ff_coeff[4:0]\nAccess: RW\nReset Value: 5'hf\nDescription:\n", "Name:dfe_ff_coeff[4:0]\nAccess: RW\nReset Value: 5'hf\nDescription:\n", '0x00']
PAGE1_NAME211 = ["Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", "Name:lqi_val\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport Link Quality Indicator measurement for each packet", '0x00']
PAGE1_NAME210 = ["Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:interpolated_sat[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", '0x00']
PAGE1_NAME209 = ["Name:interpolated_sat[11:8]\nAccess: RW\nReset Value: 4'b0\nDescription:\n调制时的插值饱和阈值配置\n超出阈值将饱和成阈值", "Name:interpolated_sat[11:8]\nAccess: RW\nReset Value: 4'b0\nDescription:\n调制时的插值饱和阈值配置\n超出阈值将饱和成阈值", "Name:interpolated_sat[11:8]\nAccess: RW\nReset Value: 4'b0\nDescription:\n调制时的插值饱和阈值配置\n超出阈值将饱和成阈值", "Name:interpolated_sat[11:8]\nAccess: RW\nReset Value: 4'b0\nDescription:\n调制时的插值饱和阈值配置\n超出阈值将饱和成阈值", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME208 = ["Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[7:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", '0x00']
PAGE1_NAME207 = ["Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[15:0]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", '0x00']
PAGE1_NAME206 = ["Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", "Name:seed[23:16]\nAccess: RW\nReset Value: 8'hff\nDescription:\n", '0x00']
PAGE1_NAME205 = ["Name:seed[27:24]\nAccess: RW\nReset Value: 4'ha\nDescription:\n28位PRBS序列初始种子\n用于产生调制测试PRBS序列", "Name:seed[27:24]\nAccess: RW\nReset Value: 4'ha\nDescription:\n28位PRBS序列初始种子\n用于产生调制测试PRBS序列", "Name:seed[27:24]\nAccess: RW\nReset Value: 4'ha\nDescription:\n28位PRBS序列初始种子\n用于产生调制测试PRBS序列", "Name:seed[27:24]\nAccess: RW\nReset Value: 4'ha\nDescription:\n28位PRBS序列初始种子\n用于产生调制测试PRBS序列", "Name:load_seed\nAccess: RW\nReset Value: 1'b0\nDescription:\n28位PRBS初始种子载入控制\n1’b0: 正常移位操作\n1'b1: 此时seed[27:0]被载入移位寄存器\n需要载入新的种子，在seed[27:0]全部写入后，往此位先写入1完成载入，再写入0放开控制。", "Name:gauss_fltr_select_gauss_rect\nAccess: RW\nReset Value: 1'b1\nDescription:\n高斯窗口/矩形窗选择\n1'b0: 矩形窗\n1‘b1: 高斯窗", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\nReserved", '0x00']
PAGE1_NAME204 = ["Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", "Name:prbs_period[7:0]\nAccess: RW\nReset Value: 8'hfa\nDescription:\n", '0x00']
PAGE1_NAME203 = ["Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", "Name:prbs_period[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPRBS序列循环周期", '0x00']
PAGE1_NAME202 = ["Name:data_src[1:0]\nAccess: RW\nReset Value: 2'b10\nDescription:\n调制数据来源\n2’d0: 00001111序列\n2'd1: PRBS序列\n2'd2: 从基带输入数据\n2'd3: 01010101序列", "Name:data_src[1:0]\nAccess: RW\nReset Value: 2'b10\nDescription:\n调制数据来源\n2’d0: 00001111序列\n2'd1: PRBS序列\n2'd2: 从基带输入数据\n2'd3: 01010101序列", "Name:NA\nAccess: RO\nReset Value: 6'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'h0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'h0\nDescription:\nReserved", '0x00']
PAGE1_NAME201 = ["Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", '0x00']
PAGE1_NAME200 = ["Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", "Name:scale_factor[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\n", '0x00']
PAGE1_NAME199 = ["Name:scale_factor[19:16]\nAccess: RW\nReset Value: 4'h1\nDescription:\n预加重后乘上的一个缩放因子\n用于调节调制频\n", "Name:scale_factor[19:16]\nAccess: RW\nReset Value: 4'h1\nDescription:\n预加重后乘上的一个缩放因子\n用于调节调制频\n", "Name:scale_factor[19:16]\nAccess: RW\nReset Value: 4'h1\nDescription:\n预加重后乘上的一个缩放因子\n用于调节调制频\n", "Name:scale_factor[19:16]\nAccess: RW\nReset Value: 4'h1\nDescription:\n预加重后乘上的一个缩放因子\n用于调节调制频\n", "Name:cdr_counter_init\nAccess: RW\nReset Value: 3'b0\nDescription:\nCDR计数器初始值", "Name:cdr_counter_init\nAccess: RW\nReset Value: 3'b0\nDescription:\nCDR计数器初始值", "Name:cdr_counter_init\nAccess: RW\nReset Value: 3'b0\nDescription:\nCDR计数器初始值", "Name:cdr_init_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nCDR（时钟数据恢复）计数器强制寄存器控制\n1’b0: CDR计数器不强制寄存器给出\n1‘b1: CDR计数器由cdr_counter_init[2:0]给出", '0x00']
PAGE1_NAME198 = ["Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", "Name:\nAccess: RW\nReset Value: 8'h3c\nDescription:\n", '0x00']
PAGE1_NAME197 = ["Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", "Name:min_peak_trough_diff\nAccess: RW\nReset Value: 8'h14\nDescription:\n", '0x00']
PAGE1_NAME196 = ["Name:en_dfe\nAccess: RW\nReset Value: 1'b1\nDescription:\nDFE使能，忽略", "Name:use_quad_mixer\nAccess: RW\nReset Value: 1'b1\nDescription:\n中频混频选择\n1‘b0: I-Q混频\n1’b1: 正交混频", "Name:oqpsk_lsb_1st\nAccess: RW\nReset Value: 1'b1\nDescription:\nOQPSK相关，忽略", "Name:rxbit_dataout_sel\nAccess: RW\nReset Value: 1'b1\nDescription:\n基带数据输出选择\n1'b0: 高4位为0，低4位为Filter输出的单比特i/q信号\n1’b1: 输出中频混频后的8位I路基带数据", "Name:qdataouten\nAccess: RW\nReset Value: 1'b0\nDescription:\n解调数据输出使能\n1’b0: 输出无效\n1‘b1: 输出有效", "Name:idataouten\nAccess: RW\nReset Value: 1'b0\nDescription:\n解调时钟输出使能\n1’b0: 输出无效\n1‘b1: 输出有效", "Name:en_cdr_in_acc_match\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:en_cdr\nAccess: RW\nReset Value: 1'b1\nDescription:\n", '0x00']
PAGE1_NAME195 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:demod_en_wide_bw\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:agg_cdr_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:dfe_en_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:gata_soft_bits\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:en_off_est_timeout\nAccess: RW\nReset Value: 1'b1\nDescription:\n", "Name:ble_long_range\nAccess: RW\nReset Value: 1'b0\nDescription:\nBLE long range(coded) mode\n1'b0: Uncoded BLE mode 1 or 2 Mbps\n1'b1: Coded long range BLE mode 1Mbps", "Name:en_drift_compensate\nAccess: RW\nReset Value: 1'b1\nDescription:\n", '0x00']
PAGE1_NAME194 = ["Name:dfe_fb_coeff\nAccess: RW\nReset Value: 6'h9\nDescription:\n", "Name:dfe_fb_coeff\nAccess: RW\nReset Value: 6'h9\nDescription:\n", "Name:dfe_fb_coeff\nAccess: RW\nReset Value: 6'h9\nDescription:\n", "Name:dfe_fb_coeff\nAccess: RW\nReset Value: 6'h9\nDescription:\n", "Name:dfe_fb_coeff\nAccess: RW\nReset Value: 6'h9\nDescription:\n", "Name:dfe_fb_coeff\nAccess: RW\nReset Value: 6'h9\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME193 = ["Name:preamble_det_thresh\nAccess: RW\nReset Value: 5'h5\nDescription:\n", "Name:preamble_det_thresh\nAccess: RW\nReset Value: 5'h5\nDescription:\n", "Name:preamble_det_thresh\nAccess: RW\nReset Value: 5'h5\nDescription:\n", "Name:preamble_det_thresh\nAccess: RW\nReset Value: 5'h5\nDescription:\n", "Name:preamble_det_thresh\nAccess: RW\nReset Value: 5'h5\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME192 = ["Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\nReserved", "Name:tx_intrplt\nAccess: RW\nReset Value: 1'b0\nDescription:\n发送插值使能\n1'b0: 插值功能关闭\n1'b1: 插值功能打开", "Name:oqpsk_ble_mode\nAccess: RW\nReset Value: 1'b0\nDescription:\nZigbee OQPSK/BLE模式选择\n1‘b0: BLE\n1'b1: Zigbee OQPSK", '0x00']
PAGE1_NAME191 = ["Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", '0x00']
PAGE1_NAME190 = ["Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\ntx频率修正", '0x00']
PAGE1_NAME189 = ["Name:frequency_correction_tx[17:16]\nAccess: RW\nReset Value: 2'b0\nDescription:\ntx频率修正", "Name:frequency_correction_tx[17:16]\nAccess: RW\nReset Value: 2'b0\nDescription:\ntx频率修正", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME188 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME187 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME186 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME185 = ["Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", '0x00']
PAGE1_NAME184 = ["Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[15:8]\nAccess: RW\nReset Value: 8'h10\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", '0x00']
PAGE1_NAME183 = ["Name:rx_frequency_offset[17:16]\nAccess: RW\nReset Value: 2'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:rx_frequency_offset[17:16]\nAccess: RW\nReset Value: 2'b0\nDescription:\n接收机中频的小数部分设置，默认为2M，且不应改变\n其计算方式为：2M/24M*2^17=10923=0x2AAB", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME182 = ["Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[7:0]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", '0x00']
PAGE1_NAME181 = ["Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[15:8]\nAccess: RW\nReset Value: 8'h0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", '0x00']
PAGE1_NAME180 = ["Name:frequency_offset_ovrdwd[17:16]\nAccess: RW\nReset Value: 2'b0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd[17:16]\nAccess: RW\nReset Value: 2'b0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出值\n当0xB3[2]=1时，小数部分从此寄存器输出，而不是通过Channel Index查表计算得到。", "Name:frequency_offset_ovrdwd_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL小数分频器的小数部分寄存器强制输出控制\n1’b0: 小数分频部分通过Channel Index查表得到\n1‘b1: 小数分频部分通过frequency_offset_ovrdwd[17:0]给出", "Name:fsynsdtxin_pol\nAccess: RW\nReset Value: 1'b1\nDescription:\n调制数据送入PLL的频率控制输入数据极性\n1’b0: 下降沿送出数据\n1‘b1: 上升沿送出数据", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nReserved", '0x00']
PAGE1_NAME179 = ["Name:gain_index\nAccess: RO\nReset Value: 3'h0\nDescription:\nAGC gain查表地址（同步后）", "Name:gain_index\nAccess: RO\nReset Value: 3'h0\nDescription:\nAGC gain查表地址（同步后）", "Name:gain_index\nAccess: RO\nReset Value: 3'h0\nDescription:\nAGC gain查表地址（同步后）", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:force_gain_idx_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx filter offset cal时，通过此位选择force_gain_idx[2:0]", "Name:force_gain_idx[2:0]\nAccess: RW\nReset Value: 3'b0\nDescription:\nrx filter offset cal时，通过此gain完成", "Name:force_gain_idx[2:0]\nAccess: RW\nReset Value: 3'b0\nDescription:\nrx filter offset cal时，通过此gain完成", "Name:force_gain_idx[2:0]\nAccess: RW\nReset Value: 3'b0\nDescription:\nrx filter offset cal时，通过此gain完成", '0x00']
PAGE1_NAME178 = ["Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nrssi结果(resolution:0.25dbm)", '0x00']
PAGE1_NAME177 = ["Name:rssi_abs_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:rssi_abs_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nrssi结果(resolution:0.25dbm)", "Name:adc_mux_cfg\nAccess: RW\nReset Value: 2'b0\nDescription:\nADC输出信号mux选择", "Name:adc_mux_cfg\nAccess: RW\nReset Value: 2'b0\nDescription:\nADC输出信号mux选择", '0x00']
PAGE1_NAME176 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME175 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME174 = ["Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[63:56]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME173 = ["Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[55:48]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME172 = ["Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[47:40]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME171 = ["Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[39:32]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME170 = ["Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[31:24]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME169 = ["Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[23:16]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME168 = ["Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[15:8]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME167 = ["Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", "Name:access_code[7:0]\nAccess: RW\nReset Value: 8'b0\nDescription:\nbt access_code[63:0]", '0x00']
PAGE1_NAME166 = ["Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", "Name:txfltrdcoc_q\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc q端调谐控制信号", '0x00']
PAGE1_NAME165 = ["Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", "Name:txfltrdcoc_i\nAccess: RW\nReset Value: 8'b0\nDescription:\ndcoc i端调谐控制信号", '0x00']
PAGE1_NAME164 = ["Name:agc_cfg_wre\nAccess: RW\nReset Value: 1'b0\nDescription:\nagc ctrl mem写使能", "Name:agc_cfg_idx\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc ctrl mem地址", "Name:agc_cfg_idx\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc ctrl mem地址", "Name:agc_cfg_idx\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc ctrl mem地址", "Name:agc_cfg_idx\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc ctrl mem地址", "Name:agc_cfg_idx\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc ctrl mem地址", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME163 = ["Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", "Name:agc_cfg\nAccess: RW\nReset Value: 8'b0\nDescription:\nagc ctrl mem配置值", '0x00']
PAGE1_NAME162 = ['Name:txdc_q\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC Q路DC offset配置', 'Name:txdc_q\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC Q路DC offset配置', 'Name:txdc_q\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC Q路DC offset配置', 'Name:txdc_q\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC Q路DC offset配置', 'Name:txdc_q\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC Q路DC offset配置', "Name:txdacq_gain\nAccess: RW\nReset Value: 3'b0\nDescription:\nDAC Q路增益控制", "Name:txdacq_gain\nAccess: RW\nReset Value: 3'b0\nDescription:\nDAC Q路增益控制", "Name:txdacq_gain\nAccess: RW\nReset Value: 3'b0\nDescription:\nDAC Q路增益控制", '0x00']
PAGE1_NAME161 = ['Name:txdc_i\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC I路DC offset配置', 'Name:txdc_i\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC I路DC offset配置', 'Name:txdc_i\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC I路DC offset配置', 'Name:txdc_i\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC I路DC offset配置', 'Name:txdc_i\nAccess: RW\nReset Value: 5‘b0\nDescription:\nDAC I路DC offset配置', "Name:txdaci_gain\nAccess: RW\nReset Value: 3'b0\nDescription:\nDAC I路增益控制", "Name:txdaci_gain\nAccess: RW\nReset Value: 3'b0\nDescription:\nDAC I路增益控制", "Name:txdaci_gain\nAccess: RW\nReset Value: 3'b0\nDescription:\nDAC I路增益控制", '0x00']
PAGE1_NAME160 = ["Name:txdacq_on\nAccess: RW\nReset Value: 1'b0\nDescription:\nDAC Q路开关", "Name:txdaci_on\nAccess: RW\nReset Value: 1'b0\nDescription:\nDAC I路开关", "Name:txfltr_en\nAccess: RW\nReset Value: 1'b0\nDescription:\n滤波器使能控制", "Name:txpa_en\nAccess: RW\nReset Value: 1'b0\nDescription:\nPA使能控制", "Name:txdac_reset\nAccess: RW\nReset Value: 1'b1\nDescription:\nDAC复位\n1‘b1：工作\n1’b0：复位", "Name:prb_ipoly_txmxrblk\nAccess: RW\nReset Value: 1'b0\nDescription:\nMixer开关管的衬底电压使能信号", "Name:prb_ipoly_txmxrvcm\nAccess: RW\nReset Value: 1'b0\nDescription:\n上变频本振LO的共模电平模块使能信号", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME159 = ["Name:txfltr_ipolyprb\nAccess: RW\nReset Value: 1'b0\nDescription:\n滤波器偏置电流减半\n1‘b0：5uA\n1’b1：2.5uA", "Name:txfltr_outgnd\nAccess: RW\nReset Value: 1'b0\nDescription:\n滤波器关断时输出零电平使能\n1‘b0：输出共模电平\n1’b1：输出零电平", "Name:txfltr_ibias\nAccess: RW\nReset Value: 2'h1\nDescription:\n滤波器偏置电流", "Name:txfltr_ibias\nAccess: RW\nReset Value: 2'h1\nDescription:\n滤波器偏置电流", "Name:txfltr_vcm\nAccess: RW\nReset Value: 3'h3\nDescription:\n滤波器共模电平trim", "Name:txfltr_vcm\nAccess: RW\nReset Value: 3'h3\nDescription:\n滤波器共模电平trim", "Name:txfltr_vcm\nAccess: RW\nReset Value: 3'h3\nDescription:\n滤波器共模电平trim", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME158 = ["Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:txfltr_tune\nAccess: RW\nReset Value: 7'b0\nDescription:\n滤波器电容阵列调谐(拟删除)", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME157 = ["Name:txfltr_gain\nAccess: RW\nReset Value: 3'h0\nDescription:\n滤波器增益控制", "Name:txfltr_gain\nAccess: RW\nReset Value: 3'h0\nDescription:\n滤波器增益控制", "Name:txfltr_gain\nAccess: RW\nReset Value: 3'h0\nDescription:\n滤波器增益控制", "Name:txmxrvcmen\nAccess: RW\nReset Value: 1'b0\nDescription:\n上变频器本振LO共模使能控制", "Name:txmxrvcm\nAccess: RW\nReset Value: 4'h0\nDescription:\n上变频本振LO的共模电平控制", "Name:txmxrvcm\nAccess: RW\nReset Value: 4'h0\nDescription:\n上变频本振LO的共模电平控制", "Name:txmxrvcm\nAccess: RW\nReset Value: 4'h0\nDescription:\n上变频本振LO的共模电平控制", "Name:txmxrvcm\nAccess: RW\nReset Value: 4'h0\nDescription:\n上变频本振LO的共模电平控制", '0x00']
PAGE1_NAME156 = ["Name:txmixr_mc\nAccess: RW\nReset Value: 4'h8\nDescription:\nTx Mixer匹配电容", "Name:txmixr_mc\nAccess: RW\nReset Value: 4'h8\nDescription:\nTx Mixer匹配电容", "Name:txmixr_mc\nAccess: RW\nReset Value: 4'h8\nDescription:\nTx Mixer匹配电容", "Name:txmixr_mc\nAccess: RW\nReset Value: 4'h8\nDescription:\nTx Mixer匹配电容", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME155 = ["Name:txpa_bias\nAccess: RW\nReset Value: 4'h8\nDescription:\nPA偏置电流控制", "Name:txpa_bias\nAccess: RW\nReset Value: 4'h8\nDescription:\nPA偏置电流控制", "Name:txpa_bias\nAccess: RW\nReset Value: 4'h8\nDescription:\nPA偏置电流控制", "Name:txpa_bias\nAccess: RW\nReset Value: 4'h8\nDescription:\nPA偏置电流控制", "Name:txpa_gain\nAccess: RW\nReset Value: 4'h0\nDescription:\nPA增益选择", "Name:txpa_gain\nAccess: RW\nReset Value: 4'h0\nDescription:\nPA增益选择", "Name:txpa_gain\nAccess: RW\nReset Value: 4'h0\nDescription:\nPA增益选择", "Name:txpa_gain\nAccess: RW\nReset Value: 4'h0\nDescription:\nPA增益选择", '0x00']
PAGE1_NAME154 = ["Name:txmxrbulk\nAccess: RW\nReset Value: 3'h0\nDescription:\n上变频upmixer开关管的衬底电压控制", "Name:txmxrbulk\nAccess: RW\nReset Value: 3'h0\nDescription:\n上变频upmixer开关管的衬底电压控制", "Name:txmxrbulk\nAccess: RW\nReset Value: 3'h0\nDescription:\n上变频upmixer开关管的衬底电压控制", "Name:txmxren\nAccess: RW\nReset Value: 1'b0\nDescription:\n上变频upmixer使能", "Name:txpa_cas\nAccess: RW\nReset Value: 3'h0\nDescription:\nPA cascode调整", "Name:txpa_cas\nAccess: RW\nReset Value: 3'h0\nDescription:\nPA cascode调整", "Name:txpa_cas\nAccess: RW\nReset Value: 3'h0\nDescription:\nPA cascode调整", "Name:txfltrbw\nAccess: RW\nReset Value: 1'b1\nDescription:\n滤波器带宽调整\n1'b1：1.5MHz\n1'b0：1MHz", '0x00']
PAGE1_NAME153 = ["Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 8'h16\nDescription:\nagc校准时长配置", '0x00']
PAGE1_NAME152 = ["Name:wait_time\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc校准时长配置", "Name:wait_time\nAccess: RW\nReset Value: 5'b0\nDescription:\nagc校准时长配置", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME151 = ["Name:pmufsynvcoldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nfsynvco ldo测试项选择控制字", "Name:pmufsynvcoldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nfsynvco ldo测试项选择控制字", "Name:pmufsynvcoldoatsten\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsynvco ldo测试使能", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:pmurxadcldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nrxadc ldo测试项选择控制字", "Name:pmurxadcldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nrxadc ldo测试项选择控制字", "Name:pmurxadcldoatsten\nAccess: RW\nReset Value: 1'b0\nDescription:\nrxadc ldo测试使能", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME150 = ["Name:freeze\nAccess: RW\nReset Value: 1'b1\nDescription:\n强制锁定agc", "Name:agc_en_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n当agc_en_sel=1时，AGC模块使能由该bit控制\n1'b1：enable\n1'b0：disable", "Name:agc_en_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\n强制RX增益寄存器控制选择\n1‘b0: RX各模块增益由AGC输出\n1’b1: AGC使能以及RX各模块增益由寄存器输出，包括：\n(x)AGC模块agc_en有r0095 bit[1]控制\n(1)LNA增益由r00f5[4:3]控制\n(2)Filter增益由r00f5[2]控制\n(3)Mixer增益由r00f5[1:0]控制", "Name:cntrl_force_gain\nAccess: RW\nReset Value: 1'b0\nDescription:\nLNA/FILTER/MIXER的增益控制选择\n1：由寄存器r0095配置\n0：由AGC模块计算所得", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME149 = ["Name:pmurffeldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nrffe ldo测试项选择控制字", "Name:pmurffeldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nrffe ldo测试项选择控制字", "Name:pmurffeldoatsten\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe ldo测试使能", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:pmuanaldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nana ldo测试项选择控制字", "Name:pmuanaldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nana ldo测试项选择控制字", "Name:pmuanaldoatsten\nAccess: RW\nReset Value: 1'b0\nDescription:\nana ldo测试使能", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME148 = ["Name:offset_est_done\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", "Name:correlation_threshold[6:0]\nAccess: RW\nReset Value: 7'h50\nDescription:\n", '0x00']
PAGE1_NAME147 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME146 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME145 = ["Name:switch_tmr_en\nAccess: RW\nReset Value: 1'b0\nDescription:\nswithc agc校准时长采用wait_time_switch的使能", "Name:switch_freeze\nAccess: RW\nReset Value: 1'b0\nDescription:\nswitch agc校准次数到达上限后，强制锁定功能的使能", "Name:iteration_limit[3:0]\nAccess: RW\nReset Value: 4'h1\nDescription:\nagc校准次数上限", "Name:iteration_limit[3:0]\nAccess: RW\nReset Value: 4'h1\nDescription:\nagc校准次数上限", "Name:iteration_limit[3:0]\nAccess: RW\nReset Value: 4'h1\nDescription:\nagc校准次数上限", "Name:iteration_limit[3:0]\nAccess: RW\nReset Value: 4'h1\nDescription:\nagc校准次数上限", "Name:iteration_freeze\nAccess: RW\nReset Value: 1'b1\nDescription:\nagc校准达到设定上限后，强制锁定功能的使能", "Name:aggressive_cdr\nAccess: RW\nReset Value: 1'b1\nDescription:\n", '0x00']
PAGE1_NAME144 = ["Name:iteration_cnt\nAccess: RO\nReset Value: 4'b0\nDescription:\nagc当前校准轮次", "Name:iteration_cnt\nAccess: RO\nReset Value: 4'b0\nDescription:\nagc当前校准轮次", "Name:iteration_cnt\nAccess: RO\nReset Value: 4'b0\nDescription:\nagc当前校准轮次", "Name:iteration_cnt\nAccess: RO\nReset Value: 4'b0\nDescription:\nagc当前校准轮次", "Name:gain_index_cont\nAccess: RO\nReset Value: 3'h0\nDescription:\nagc gain查表地址(实时)", "Name:gain_index_cont\nAccess: RO\nReset Value: 3'h0\nDescription:\nagc gain查表地址(实时)", "Name:gain_index_cont\nAccess: RO\nReset Value: 3'h0\nDescription:\nagc gain查表地址(实时)", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME143 = ["Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", "Name:switch_cnt\nAccess: RO\nReset Value: 8'b0\nDescription:\nswitch agc校准下当前校准轮次", '0x00']
PAGE1_NAME142 = ["Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", "Name:switch_freeze_cnt\nAccess: RW\nReset Value: 8'h1\nDescription:\nswitch agc校准次数上限", '0x00']
PAGE1_NAME141 = ["Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[7:0]\nAccess: RW\nReset Value: 8'h80\nDescription:\nswitch模式下agc校准时长配置", '0x00']
PAGE1_NAME140 = ["Name:wati_time_switch[12:8]\nAccess: RW\nReset Value: 5'b0\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[12:8]\nAccess: RW\nReset Value: 5'b0\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[12:8]\nAccess: RW\nReset Value: 5'b0\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[12:8]\nAccess: RW\nReset Value: 5'b0\nDescription:\nswitch模式下agc校准时长配置", "Name:wati_time_switch[12:8]\nAccess: RW\nReset Value: 5'b0\nDescription:\nswitch模式下agc校准时长配置", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME139 = ["Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", "Name:ed_val_sampld[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\n模块计算结果的实时值\r\nassign ed_val_sampld[7:0] = ed_val[7:0]\r\ned_val为状态机结束时的终值", '0x00']
PAGE1_NAME138 = ["Name:ed_val_sampld[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\n模块计算结果的实时值\nassign ed_val_sampld[13:8] = ed_val[13:8]\ned_val为状态机结束时的终值", "Name:ed_val_sampld[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\n模块计算结果的实时值\nassign ed_val_sampld[13:8] = ed_val[13:8]\ned_val为状态机结束时的终值", "Name:ed_val_sampld[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\n模块计算结果的实时值\nassign ed_val_sampld[13:8] = ed_val[13:8]\ned_val为状态机结束时的终值", "Name:ed_val_sampld[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\n模块计算结果的实时值\nassign ed_val_sampld[13:8] = ed_val[13:8]\ned_val为状态机结束时的终值", "Name:ed_val_sampld[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\n模块计算结果的实时值\nassign ed_val_sampld[13:8] = ed_val[13:8]\ned_val为状态机结束时的终值", "Name:ed_val_sampld[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\n模块计算结果的实时值\nassign ed_val_sampld[13:8] = ed_val[13:8]\ned_val为状态机结束时的终值", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:ed_val_valid_sampld\nAccess: RO\nReset Value: 1'b0\nDescription:\n结果有效标志位的实时值，\nassign ed_val_valid_sampld = ed_val_valid\ned_val_valid为状态机结束时的终值", '0x00']
PAGE1_NAME137 = ["Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", '0x00']
PAGE1_NAME136 = ["Name:ed_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val[13:8]\nAccess: RO\nReset Value: 6'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n模块最终计算结果", "Name:ed_val_valid\nAccess: RO\nReset Value: 1'b0\nDescription:\nreport the receiver Energy Detection measurement with valid/invalid value indicator flag captured when spi cs is low\n结果有效标志位，1为有效", "Name:cs_flag\nAccess: RO\nReset Value: 1'b0\nDescription:\ncarrier sense flag indicator out from AGC captured when spi cs is low\n空闲信道检测结果\n1’b1：无空闲信道\n1‘b0：有空闲信道", '0x00']
PAGE1_NAME135 = ["Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 8'h0\nDescription:\nrssi value out[7:0] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\r\nit is reported in dBm with resolution of 0.25dB", '0x00']
PAGE1_NAME134 = ["Name:rssi_out\nAccess: RO\nReset Value: 6'h0\nDescription:\nrssi value out[13:8] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 6'h0\nDescription:\nrssi value out[13:8] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 6'h0\nDescription:\nrssi value out[13:8] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 6'h0\nDescription:\nrssi value out[13:8] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 6'h0\nDescription:\nrssi value out[13:8] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\nit is reported in dBm with resolution of 0.25dB", "Name:rssi_out\nAccess: RO\nReset Value: 6'h0\nDescription:\nrssi value out[13:8] from AGC sampled by agc_enable\nThe Received Signal Strength Indicator(RSSI) value.\nit is reported in dBm with resolution of 0.25dB", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME133 = ["Name:rssi_iqfltr_coeff\nAccess: RW\nReset Value: 6'h3f\nDescription:\nHPF coeff in RSSI", "Name:rssi_iqfltr_coeff\nAccess: RW\nReset Value: 6'h3f\nDescription:\nHPF coeff in RSSI", "Name:rssi_iqfltr_coeff\nAccess: RW\nReset Value: 6'h3f\nDescription:\nHPF coeff in RSSI", "Name:rssi_iqfltr_coeff\nAccess: RW\nReset Value: 6'h3f\nDescription:\nHPF coeff in RSSI", "Name:rssi_iqfltr_coeff\nAccess: RW\nReset Value: 6'h3f\nDescription:\nHPF coeff in RSSI", "Name:rssi_iqfltr_coeff\nAccess: RW\nReset Value: 6'h3f\nDescription:\nHPF coeff in RSSI", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n\xa0", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n\xa0", '0x00']
PAGE1_NAME132 = ["Name:rssi_iqfltr_mode\nAccess: RW\nReset Value: 3'h1\nDescription:\nHPF mode in RSSI\n3‘b000: bypass the filter\n3’b001: filter fail, track rise\n3‘b010: filter rise, track fail\n3'b011: filter rise & fall\n3'b100: bypass the filter\n3'b101: peak detector\n3'b110: trough detector\n3'b111: filter rise & fall", "Name:rssi_iqfltr_mode\nAccess: RW\nReset Value: 3'h1\nDescription:\nHPF mode in RSSI\n3‘b000: bypass the filter\n3’b001: filter fail, track rise\n3‘b010: filter rise, track fail\n3'b011: filter rise & fall\n3'b100: bypass the filter\n3'b101: peak detector\n3'b110: trough detector\n3'b111: filter rise & fall", "Name:rssi_iqfltr_mode\nAccess: RW\nReset Value: 3'h1\nDescription:\nHPF mode in RSSI\n3‘b000: bypass the filter\n3’b001: filter fail, track rise\n3‘b010: filter rise, track fail\n3'b011: filter rise & fall\n3'b100: bypass the filter\n3'b101: peak detector\n3'b110: trough detector\n3'b111: filter rise & fall", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME131 = ["Name:pmutop_atst_sel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nPMU测试项选择控制字", "Name:pmutop_atst_sel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nPMU测试项选择控制字", "Name:pmutop_atst_en\nAccess: RW\nReset Value: 1'b0\nDescription:\nPMU测试使能", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:pmufsynldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nfsyn ldo测试项选择控制字", "Name:pmufsynldoatstsel[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nfsyn ldo测试项选择控制字", "Name:pmufsynldoatsten\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn ldo测试使能", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME130 = ["Name:tx_lowltncy\nAccess: RW\nReset Value: 1'b0\nDescription:\n1'b0: defaut zigbee modulator latency\n1'b1: reduced zigbee modulatro latency", "Name:rx_lowltncy\nAccess: RW\nReset Value: 1'b0\nDescription:\n1'b0: defaut zigbee demodulator latency\n1'b1: reduced zigbee demodulatro latency", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME129 = ["Name:soft_rst_n\nAccess: RW\nReset Value: 1'b1\nDescription:\n系统软件复位", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE1_NAME128 = ["Name:ce_bit\nAccess: RW\nReset Value: 1'b0\nDescription:\nThis bit is equivalent to the external CE pin.\nIt is an alternative way for controlling the chip without the need to control the external CE pin", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", "Name:sfdmatch_threshold\nAccess: RW\nReset Value: 7'h34\nDescription:\n", '0x00']
PAGE1_NAME127 = ["Name:bitrate\nAccess: RW\nReset Value: 1'b0\nDescription:\nBLE 1M/2M选择\n1’b0: BLE 1M\n1'b1: BLE 2M", "Name:tx_osr\nAccess: RW\nReset Value: 1'b1\nDescription:\nTX时的过采样率（影响高斯系数值）\n1’b0: OSR=8\n1'b1: OSR=16", "Name:gauss_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n高斯系数表使用寄存器堆的控制信号\n1’b0: 高斯系数以常数形式定义在设计中\n1‘b1: 高斯系数使用存放在寄存器堆中的配置值（寄存器堆通过0xD6~0xD8的寄存器的操作进行配置）", "Name:gauss_bt\nAccess: RW\nReset Value: 1'b0\nDescription:\n高斯调制时的BT参数\n1‘b0: BT=0.5\n1'b1: BT=1", "Name:preemph_coeff_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\n预加重系数选择", "Name:NA\nAccess: \nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: \nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: \nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME126 = ["Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:rf_ch\nAccess: RW\nReset Value: 7'h0\nDescription:\nRF通道号", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME125 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME124 = ["Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_64\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", '0x00']
PAGE1_NAME123 = ["Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", "Name:rsrvd_rw_63\nAccess: RW\nReset Value: 8'hff\nDescription:\n保留", '0x00']
PAGE1_NAME122 = ["Name:rsrvd_rw_62[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nrxofstcmptrim[1:0]，dcoc trim信号", "Name:rsrvd_rw_62[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nrxofstcmptrim[1:0]，dcoc trim信号", "Name:rsrvd_rw_62[7:2]\nAccess: RW\nReset Value: 6'h0\nDescription:\n保留", "Name:rsrvd_rw_62[7:2]\nAccess: RW\nReset Value: 6'h0\nDescription:\n保留", "Name:rsrvd_rw_62[7:2]\nAccess: RW\nReset Value: 6'h0\nDescription:\n保留", "Name:rsrvd_rw_62[7:2]\nAccess: RW\nReset Value: 6'h0\nDescription:\n保留", "Name:rsrvd_rw_62[7:2]\nAccess: RW\nReset Value: 6'h0\nDescription:\n保留", "Name:rsrvd_rw_62[7:2]\nAccess: RW\nReset Value: 6'h0\nDescription:\n保留", '0x00']
PAGE1_NAME121 = ["Name:rsrvd_rw_61[2:0]\nAccess: RW\nReset Value: 3'h3\nDescription:\nfsynvconmosbias[2:0]，VCO负阻偏置电压调节控制位", "Name:rsrvd_rw_61[2:0]\nAccess: RW\nReset Value: 3'h3\nDescription:\nfsynvconmosbias[2:0]，VCO负阻偏置电压调节控制位", "Name:rsrvd_rw_61[2:0]\nAccess: RW\nReset Value: 3'h3\nDescription:\nfsynvconmosbias[2:0]，VCO负阻偏置电压调节控制位", "Name:rsrvd_rw_61[3]\nAccess: RW\nReset Value: 1'h0\nDescription:\nrx_adctst_en，adc Test Bi-Buffer en", "Name:rsrvd_rw_61[7:4]\nAccess: RW\nReset Value: 4'h0\nDescription:\n保留", "Name:rsrvd_rw_61[7:4]\nAccess: RW\nReset Value: 4'h0\nDescription:\n保留", "Name:rsrvd_rw_61[7:4]\nAccess: RW\nReset Value: 4'h0\nDescription:\n保留", "Name:rsrvd_rw_61[7:4]\nAccess: RW\nReset Value: 4'h0\nDescription:\n保留", '0x00']
PAGE1_NAME120 = ["Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_60\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", '0x00']
PAGE1_NAME119 = ["Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", "Name:rsrvd_ro_5f\nAccess: RO\nReset Value: 8'b0\nDescription:\nforce 8'd0", '0x00']
PAGE1_NAME118 = ["Name:tmuxdiginen[0]\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsynfbdivtsten，多模分频输出测试使能", "Name:tmuxdiginen[1]\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyncaldivtsten，Cal Divider输出测试使能", "Name:tmuxdiginen[7:2]\nAccess: RW\nReset Value: 6'b0\nDescription:\n保留", "Name:tmuxdiginen[7:2]\nAccess: RW\nReset Value: 6'b0\nDescription:\n保留", "Name:tmuxdiginen[7:2]\nAccess: RW\nReset Value: 6'b0\nDescription:\n保留", "Name:tmuxdiginen[7:2]\nAccess: RW\nReset Value: 6'b0\nDescription:\n保留", "Name:tmuxdiginen[7:2]\nAccess: RW\nReset Value: 6'b0\nDescription:\n保留", "Name:tmuxdiginen[7:2]\nAccess: RW\nReset Value: 6'b0\nDescription:\n保留", '0x00']
PAGE1_NAME117 = ["Name:tmuxanainen[0]\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsynlftsten，VCO控制电源测试使能", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", "Name:tmuxanainen[7:1]\nAccess: RW\nReset Value: 7'b0\nDescription:\n保留", '0x00']
PAGE1_NAME116 = ['Name:txpaen_delay_value\nAccess: RW\nReset Value: 4’h3\nDescription:\ntx pa enalbe信号延迟时间配置', 'Name:txpaen_delay_value\nAccess: RW\nReset Value: 4’h3\nDescription:\ntx pa enalbe信号延迟时间配置', 'Name:txpaen_delay_value\nAccess: RW\nReset Value: 4’h3\nDescription:\ntx pa enalbe信号延迟时间配置', 'Name:txpaen_delay_value\nAccess: RW\nReset Value: 4’h3\nDescription:\ntx pa enalbe信号延迟时间配置', "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME115 = ["Name:rxmxren_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nMixer使能信号\n1'b1 : enable\r\n1'b0 : disable", "Name:rxmxren_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nMixer使能信号选择\n1'b1 : rxmxren_ovrd\n1'b0 : rxmxren使能信号，或者rxmxren使能信号4个16MHz时钟周期的延迟", "Name:rxfltren_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nLPF使能信号", "Name:rxfltren_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nLPF使能信号选择\n1'b1 : rxfltren_ovrd\n1'b0 : rxfltren信号，或者rxfltren信号的1us延迟", "Name:txpaendly_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\ntx pa enalble delay，软件配置位", "Name:txpaendly_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nta pa enable deley选择\n1'b1：txpaendly_ovrd\n1'b0：由txpaen 0x70 bit[4]和0x73 bits[3:0]决定", "Name:rxmxrvcmen_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nmixer共模使能控制", "Name:rxmxrvcmen_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrxmxrvcmen的选择\n1‘b1: rxmxrvcmen_ovrd\n1'b0: rxmxren使能信号，或者rxmxren使能信号4个16MHz时钟周期的延迟", '0x00']
PAGE1_NAME114 = ["Name:rxadctestmode\nAccess: RW\nReset Value: 1'b0\nDescription:\nSAR ADC测试使能信号\n1'b0: disable\r\n1'b1: enable", "Name:rxadcclksel\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX ADC时钟沿选择\n1‘b0: ADC时钟下降沿产生数据，后级用上升延采样\n1’b1: ADC时钟上升沿产生数据，后级用下降沿采样", "Name:rxadc2mhzen_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\n", "Name:rxadc2mhzen_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX ADC时钟选择\n1'b0: 1/2 XO频率\n1‘b1: XO频率", "Name:fsynxorxadcclken\nAccess: RW\nReset Value: 1'b0\nDescription:\n晶振ADC时钟输出使能信号\n1'b0: disable\n1'b1: enable", "Name:rxadcen\nAccess: RW\nReset Value: 1'b0\nDescription:\nSAR ADC使能信号\n1'b0: disable\n1'b1: enable", "Name:use_adcfifo\nAccess: RW\nReset Value: 1'b0\nDescription:\n是否使用AFIFO进行ADC数据的同步\n1'b0: 不使用\n1'b1: 使用", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME113 = ["Name:rfmtchntwkc2val\nAccess: RW\nReset Value: 3'h7\nDescription:\nMatch Network Configuration2", "Name:rfmtchntwkc2val\nAccess: RW\nReset Value: 3'h7\nDescription:\nMatch Network Configuration2", "Name:rfmtchntwkc2val\nAccess: RW\nReset Value: 3'h7\nDescription:\nMatch Network Configuration2", "Name:rfmtchntwkc1val\nAccess: RW\nReset Value: 2'h3\nDescription:\nMatch Network Configuration1", "Name:rfmtchntwkc1val\nAccess: RW\nReset Value: 2'h3\nDescription:\nMatch Network Configuration1", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:txpaen\nAccess: RW\nReset Value: 1'b0\nDescription:\nTX PA ENABLE\n1'b0: not enabled\n1'b1: enabled", '0x00']
PAGE1_NAME112 = ["Name:rxfltribias\nAccess: RW\nReset Value: 2'b1\nDescription:\nRX Filter偏置电流", "Name:rxfltribias\nAccess: RW\nReset Value: 2'b1\nDescription:\nRX Filter偏置电流", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", 'Name:rxfltrvcm\nAccess: RW\nReset Value: 3‘h3\nDescription:\n滤波器共模电平trim信号', 'Name:rxfltrvcm\nAccess: RW\nReset Value: 3‘h3\nDescription:\n滤波器共模电平trim信号', 'Name:rxfltrvcm\nAccess: RW\nReset Value: 3‘h3\nDescription:\n滤波器共模电平trim信号', "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME111 = ["Name:pmurffeldoendly_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO delay控制信号", "Name:pmurffeldoendly_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO delay控制信号选择\n1‘b1: pmurffeldoendly_ovrd\n1'b0: 默认不使用delay控制信号", "Name:pmurffeldoen\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO线性压降使能信号\nRF Front End LDO ENABLE\n1'b0: not enabled\n1'b1: enabled", "Name:pmurffeldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nrffe LDO输出trimming控制字", "Name:pmurffeldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nrffe LDO输出trimming控制字", "Name:pmurffeldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nrffe LDO输出trimming控制字", "Name:pmurffeldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nrffe LDO输出trimming控制字", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME110 = ["Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", "Name:fsynxofinetune\nAccess: RW\nReset Value: 8'h80\nDescription:\nXO电容阵列细调", '0x00']
PAGE1_NAME109 = ["Name:fsynxocoarsetune\nAccess: RW\nReset Value: 4'h5\nDescription:\nXO电容阵列粗调", "Name:fsynxocoarsetune\nAccess: RW\nReset Value: 4'h5\nDescription:\nXO电容阵列粗调", "Name:fsynxocoarsetune\nAccess: RW\nReset Value: 4'h5\nDescription:\nXO电容阵列粗调", "Name:fsynxocoarsetune\nAccess: RW\nReset Value: 4'h5\nDescription:\nXO电容阵列粗调", "Name:fsynxotxdacclken\nAccess: RW\nReset Value: 1'b0\nDescription:\nTXDAC时钟使能", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME108 = ["Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", '0x00']
PAGE1_NAME107 = ["Name:implsgencnfgclkcycl[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:implsgencnfgclkcycl[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nimpulse generator在calibration enable被拉高后\n再经过多长时间出现脉冲", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME106 = ["Name:rfmtchntwkc2val_tx\nAccess: RW\nReset Value: 3'h0\nDescription:\nMatch Network Configuration2_tx", "Name:rfmtchntwkc2val_tx\nAccess: RW\nReset Value: 3'h0\nDescription:\nMatch Network Configuration2_tx", "Name:rfmtchntwkc2val_tx\nAccess: RW\nReset Value: 3'h0\nDescription:\nMatch Network Configuration2_tx", "Name:rfmtchntwkc1val_tx\nAccess: RW\nReset Value: 2'h0\nDescription:\nMatch Network Configuration1_tx", "Name:rfmtchntwkc1val_tx\nAccess: RW\nReset Value: 2'h0\nDescription:\nMatch Network Configuration1_tx", "Name:NA\nAccess: RO\nReset Value: 3'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'h0\nDescription:\n", '0x00']
PAGE1_NAME105 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME104 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME103 = ["Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:rxmxrcm\nAccess: RW\nReset Value: 3'h3\nDescription:\nLO的共模电平控制\n111: 737mV  110: 711mV\n101: 674mV  100: 631mV\n011: 590mV  010: 547mV\n001: 505mV  000: 570mV", "Name:rxmxrcm\nAccess: RW\nReset Value: 3'h3\nDescription:\nLO的共模电平控制\n111: 737mV  110: 711mV\n101: 674mV  100: 631mV\n011: 590mV  010: 547mV\n001: 505mV  000: 570mV", "Name:rxmxrcm\nAccess: RW\nReset Value: 3'h3\nDescription:\nLO的共模电平控制\n111: 737mV  110: 711mV\n101: 674mV  100: 631mV\n011: 590mV  010: 547mV\n001: 505mV  000: 570mV", "Name:rxmxren\nAccess: RW\nReset Value: 1'b0\nDescription:\nMixer使能信号\n1'b0: disable\n1'b1: enable", '0x00']
PAGE1_NAME102 = ["Name:pmurffeldobypass\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO bypass信号，bypass信号优先级高于pmurffeldoen", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE1_NAME101 = ["Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:rxfltrbw\nAccess: RW\nReset Value: 1'b1\nDescription:\n滤波器带宽选择\n1'b1：1.5MHz\n1'b0：1MHz", "Name:rxfltr2mhz_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx fliter 2mhz选择\n1'b1：rxfltr2mhz_ovrd\n1'b0：bitrate 0x7E bit[0]", "Name:rxfltr2mhz_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx fliter 2mhz使能，软件配置位\nBLE 1M/2M选择\r\n1’b0: BLE 1M\r\n1'b1: BLE 2M", "Name:rxfltren\nAccess: RW\nReset Value: 1'b0\nDescription:\nLPF使能信号", '0x00']
PAGE1_NAME100 = ["Name:txpapoutres\nAccess: RW\nReset Value: 6'h19\nDescription:\nTransmission(TX) Output Power Programmability.The output power of the Power Amplifier.\n\n         0-dBm Mode(dBm)      6-dBm Mode(dBm)\n0x00            -19.9                            -16.3       \n0x01            -19.2                            -15.6\n0x02            -18.3                            -17.4\n0x03            -17.4                            -14.0\n0x04            -16.5                            -13.3\n0x05            -15.6                            -12.6\n0x06            -14.8                            -11.9\n0x07            -13.8                            -11.1\n0x08            -12.9                            -10.3\n0x09            -12.1                            -9.5\n0x0A            -11.2                            -8.9\n0x0B            -10.4                            -8.1\n0x0C            -9.4                              -7.3\n0x0D            -8.5                              -6.5\n0x0E             -7.7                              -5.7\n0x0F             -6.8                              -4.9  \n0x10             -5.9                              -4.1\n0x11             -5.1                              -3.3\n0x12             -4.3                              -2.4\n0x13             -3.4                              -1.5\n0x14             -2.6                              -0.5\n0x15             -1.9                                0.8\n0x16             -1.2                                2.4\n0x17             -0.8                                4.5\n0x18             -0.6                                5.2\n0x19             -0.4                                5.5", "Name:txpapoutres\nAccess: RW\nReset Value: 6'h19\nDescription:\nTransmission(TX) Output Power Programmability.The output power of the Power Amplifier.\n\n         0-dBm Mode(dBm)      6-dBm Mode(dBm)\n0x00            -19.9                            -16.3       \n0x01            -19.2                            -15.6\n0x02            -18.3                            -17.4\n0x03            -17.4                            -14.0\n0x04            -16.5                            -13.3\n0x05            -15.6                            -12.6\n0x06            -14.8                            -11.9\n0x07            -13.8                            -11.1\n0x08            -12.9                            -10.3\n0x09            -12.1                            -9.5\n0x0A            -11.2                            -8.9\n0x0B            -10.4                            -8.1\n0x0C            -9.4                              -7.3\n0x0D            -8.5                              -6.5\n0x0E             -7.7                              -5.7\n0x0F             -6.8                              -4.9  \n0x10             -5.9                              -4.1\n0x11             -5.1                              -3.3\n0x12             -4.3                              -2.4\n0x13             -3.4                              -1.5\n0x14             -2.6                              -0.5\n0x15             -1.9                                0.8\n0x16             -1.2                                2.4\n0x17             -0.8                                4.5\n0x18             -0.6                                5.2\n0x19             -0.4                                5.5", "Name:txpapoutres\nAccess: RW\nReset Value: 6'h19\nDescription:\nTransmission(TX) Output Power Programmability.The output power of the Power Amplifier.\n\n         0-dBm Mode(dBm)      6-dBm Mode(dBm)\n0x00            -19.9                            -16.3       \n0x01            -19.2                            -15.6\n0x02            -18.3                            -17.4\n0x03            -17.4                            -14.0\n0x04            -16.5                            -13.3\n0x05            -15.6                            -12.6\n0x06            -14.8                            -11.9\n0x07            -13.8                            -11.1\n0x08            -12.9                            -10.3\n0x09            -12.1                            -9.5\n0x0A            -11.2                            -8.9\n0x0B            -10.4                            -8.1\n0x0C            -9.4                              -7.3\n0x0D            -8.5                              -6.5\n0x0E             -7.7                              -5.7\n0x0F             -6.8                              -4.9  \n0x10             -5.9                              -4.1\n0x11             -5.1                              -3.3\n0x12             -4.3                              -2.4\n0x13             -3.4                              -1.5\n0x14             -2.6                              -0.5\n0x15             -1.9                                0.8\n0x16             -1.2                                2.4\n0x17             -0.8                                4.5\n0x18             -0.6                                5.2\n0x19             -0.4                                5.5", "Name:txpapoutres\nAccess: RW\nReset Value: 6'h19\nDescription:\nTransmission(TX) Output Power Programmability.The output power of the Power Amplifier.\n\n         0-dBm Mode(dBm)      6-dBm Mode(dBm)\n0x00            -19.9                            -16.3       \n0x01            -19.2                            -15.6\n0x02            -18.3                            -17.4\n0x03            -17.4                            -14.0\n0x04            -16.5                            -13.3\n0x05            -15.6                            -12.6\n0x06            -14.8                            -11.9\n0x07            -13.8                            -11.1\n0x08            -12.9                            -10.3\n0x09            -12.1                            -9.5\n0x0A            -11.2                            -8.9\n0x0B            -10.4                            -8.1\n0x0C            -9.4                              -7.3\n0x0D            -8.5                              -6.5\n0x0E             -7.7                              -5.7\n0x0F             -6.8                              -4.9  \n0x10             -5.9                              -4.1\n0x11             -5.1                              -3.3\n0x12             -4.3                              -2.4\n0x13             -3.4                              -1.5\n0x14             -2.6                              -0.5\n0x15             -1.9                                0.8\n0x16             -1.2                                2.4\n0x17             -0.8                                4.5\n0x18             -0.6                                5.2\n0x19             -0.4                                5.5", "Name:txpapoutres\nAccess: RW\nReset Value: 6'h19\nDescription:\nTransmission(TX) Output Power Programmability.The output power of the Power Amplifier.\n\n         0-dBm Mode(dBm)      6-dBm Mode(dBm)\n0x00            -19.9                            -16.3       \n0x01            -19.2                            -15.6\n0x02            -18.3                            -17.4\n0x03            -17.4                            -14.0\n0x04            -16.5                            -13.3\n0x05            -15.6                            -12.6\n0x06            -14.8                            -11.9\n0x07            -13.8                            -11.1\n0x08            -12.9                            -10.3\n0x09            -12.1                            -9.5\n0x0A            -11.2                            -8.9\n0x0B            -10.4                            -8.1\n0x0C            -9.4                              -7.3\n0x0D            -8.5                              -6.5\n0x0E             -7.7                              -5.7\n0x0F             -6.8                              -4.9  \n0x10             -5.9                              -4.1\n0x11             -5.1                              -3.3\n0x12             -4.3                              -2.4\n0x13             -3.4                              -1.5\n0x14             -2.6                              -0.5\n0x15             -1.9                                0.8\n0x16             -1.2                                2.4\n0x17             -0.8                                4.5\n0x18             -0.6                                5.2\n0x19             -0.4                                5.5", "Name:txpapoutres\nAccess: RW\nReset Value: 6'h19\nDescription:\nTransmission(TX) Output Power Programmability.The output power of the Power Amplifier.\n\n         0-dBm Mode(dBm)      6-dBm Mode(dBm)\n0x00            -19.9                            -16.3       \n0x01            -19.2                            -15.6\n0x02            -18.3                            -17.4\n0x03            -17.4                            -14.0\n0x04            -16.5                            -13.3\n0x05            -15.6                            -12.6\n0x06            -14.8                            -11.9\n0x07            -13.8                            -11.1\n0x08            -12.9                            -10.3\n0x09            -12.1                            -9.5\n0x0A            -11.2                            -8.9\n0x0B            -10.4                            -8.1\n0x0C            -9.4                              -7.3\n0x0D            -8.5                              -6.5\n0x0E             -7.7                              -5.7\n0x0F             -6.8                              -4.9  \n0x10             -5.9                              -4.1\n0x11             -5.1                              -3.3\n0x12             -4.3                              -2.4\n0x13             -3.4                              -1.5\n0x14             -2.6                              -0.5\n0x15             -1.9                                0.8\n0x16             -1.2                                2.4\n0x17             -0.8                                4.5\n0x18             -0.6                                5.2\n0x19             -0.4                                5.5", "Name:NA\nAccess: RO\nReset Value: 2'b00\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b00\nDescription:\n", '0x00']
PAGE1_NAME99 = ["Name:rxlnaicore\nAccess: RW\nReset Value: 3'b100\nDescription:\nLNA电流偏置控制信号", "Name:rxlnaicore\nAccess: RW\nReset Value: 3'b100\nDescription:\nLNA电流偏置控制信号", "Name:rxlnaicore\nAccess: RW\nReset Value: 3'b100\nDescription:\nLNA电流偏置控制信号", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:rxlnaen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX LNA ENABLE\n1'b0: RX LNA not enabled\n1'b1: RX LNA enabled", '0x00']
PAGE1_NAME98 = ["Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:fsynxosucntred\nAccess: RW\nReset Value: 1'b1\nDescription:\n晶振数字deglitch使能控制\n1'b0: disable\r\n1'b1: enable", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsynxosuenhen\nAccess: RW\nReset Value: 1'b1\nDescription:\n晶振快速启动使能控制\n1'b0: disable\n1'b1: enable", '0x00']
PAGE1_NAME97 = ["Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", 'Name:pmurxadcldovtrim\nAccess: RW\nReset Value: 4‘h8\nDescription:\n连到PMU的digctrl_pmurxadcldovtrim_3v[3:0]\nrx adc LDO输出trimming控制字', 'Name:pmurxadcldovtrim\nAccess: RW\nReset Value: 4‘h8\nDescription:\n连到PMU的digctrl_pmurxadcldovtrim_3v[3:0]\nrx adc LDO输出trimming控制字', 'Name:pmurxadcldovtrim\nAccess: RW\nReset Value: 4‘h8\nDescription:\n连到PMU的digctrl_pmurxadcldovtrim_3v[3:0]\nrx adc LDO输出trimming控制字', 'Name:pmurxadcldovtrim\nAccess: RW\nReset Value: 4‘h8\nDescription:\n连到PMU的digctrl_pmurxadcldovtrim_3v[3:0]\nrx adc LDO输出trimming控制字', '0x00']
PAGE1_NAME96 = ['Name:NA\nAccess: RO\nReset Value: 2’b0\nDescription:\n', 'Name:NA\nAccess: RO\nReset Value: 2’b0\nDescription:\n', "Name:pmurxadcldoprbiptatref\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx adc LDO测试信号", "Name:pmurxadcldoiprbptatcore\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx adc LDO测试信号", "Name:pmurxadcldoendly_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx adc LDO delay控制信号", "Name:pmurxadcldoendly_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx adc LDO delay控制信号选择\n1‘b1: pmurxadcldoendly_ovrd\n1'b0: 默认不使用delay控制信号", "Name:pmurxadcldobypass\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx adc LDO bypass信号，bypass信号优先级高于pmurxadcldoen", "Name:pmurxadcldoen\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx adc LDO线性压降使能信号", '0x00']
PAGE1_NAME95 = ["Name:pmuptatcorefltrshrtovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPTAT 模块偏置软起关闭信号", "Name:pmuptatcorefltractovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPTAT 模块偏置软起开启信号", "Name:pmuptatcoreenovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPTAT 模块使能信号", "Name:pmuptatcorebyp\nAccess: RW\nReset Value: 2'b0\nDescription:\nPTAT bypass功能控制信号", "Name:pmuptatcorebyp\nAccess: RW\nReset Value: 2'b0\nDescription:\nPTAT bypass功能控制信号", "Name:pmufsynvcoldoprbiptatref\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO测试信号", "Name:pmufsynvcoldoiprbptatcore\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO测试信号", "Name:pmufsynldoprbiptatref\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn LDO测试信号", '0x00']
PAGE1_NAME94 = ["Name:pmufsynvcoldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn vco LDO输出trimming控制字", "Name:pmufsynvcoldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn vco LDO输出trimming控制字", "Name:pmufsynvcoldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn vco LDO输出trimming控制字", "Name:pmufsynvcoldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn vco LDO输出trimming控制字", "Name:pmufsynvcoldoendly_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO delay控制信号", "Name:pmufsynvcoldoendly_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO delay控制信号选择\n1‘b1: pmufsynvcoldoendly_ovrd\n1'b0: 默认不使用delay控制信号", "Name:pmufsynvcoldobypass\nAccess: RW\nReset Value: 1'b0\nDescription:\nFSYN VCO LDO ENABLE\n1'b0: FSYN VCO LDO not enabled\n1'b1: FSYN VCO LDO enabled\nfsyn vco LDO bypass信号，bypass信号优先级高于pmufsynvcoldoenovrd", "Name:pmufsynvcoldoen\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO线性压降使能信号", '0x00']
PAGE1_NAME93 = ["Name:pmufsynldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn LDO输出trimming控制字", "Name:pmufsynldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn LDO输出trimming控制字", "Name:pmufsynldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn LDO输出trimming控制字", "Name:pmufsynldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nfsyn LDO输出trimming控制字", "Name:pmufsynldobypass\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn LDO bypass信号，bypass信号优先级高于pmufsynldoenovrd", "Name:pmufsynvcoldoibst_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO偏置电流增加控制信号", "Name:pmufsynvcoldoibst_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn vco LDO偏置电流增加控制信号的选择\n1‘b1: pmufsynvcoldoibst_ovrd\n1'b0: LDO delay控制信号的取反（!pmufsynvcoldoendly）", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME92 = ["Name:pmuanaldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nana LDO输出trimming控制字", "Name:pmuanaldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nana LDO输出trimming控制字", "Name:pmuanaldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nana LDO输出trimming控制字", "Name:pmuanaldovtrim\nAccess: RW\nReset Value: 4'h8\nDescription:\nana LDO输出trimming控制字", "Name:pmuanaldoendly_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO delay控制信号", "Name:pmuanaldoendly_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO delay控制信号选择\n1‘b1: pmuanaldoendly_ovrd\n1'b0: 默认不使用delay控制信号", "Name:pmuanaldobypass\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO bypass信号，bypass信号优先级高于pmuanaldoen", "Name:pmuanaldoen\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO线性压降使能信号", '0x00']
PAGE1_NAME91 = ["Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:pmupolyen\nAccess: RW\nReset Value: 1'b0\nDescription:\nZTAT模块使能", '0x00']
PAGE1_NAME90 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsynvcoendly_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO使能延迟，软件配置位", "Name:fsynvcoendly_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO使能延迟选择\n1'b1：fsynvcoendly_ovrd\n1'b0：u_power_group进行延迟操作，5us延迟", "Name:fsynvcoicore\nAccess: RW\nReset Value: 4'h7\nDescription:\nVCO偏置电流大小控制位", "Name:fsynvcoicore\nAccess: RW\nReset Value: 4'h7\nDescription:\nVCO偏置电流大小控制位", "Name:fsynvcoicore\nAccess: RW\nReset Value: 4'h7\nDescription:\nVCO偏置电流大小控制位", "Name:fsynvcoicore\nAccess: RW\nReset Value: 4'h7\nDescription:\nVCO偏置电流大小控制位", '0x00']
PAGE1_NAME89 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsynvcovarbias\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO变容管偏置电压调节控制位", "Name:fsynvcovarbias\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO变容管偏置电压调节控制位", "Name:fsynvcovarbias\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO变容管偏置电压调节控制位", "Name:fsynvcovarbias\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO变容管偏置电压调节控制位", "Name:fsynvcoen_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO使能控制\n1'b0: disable\n1'b1: enable", "Name:fsynvcoen_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsynvcoen信号的选择(数值到模拟)\n1’b1: fsynvcoen_ovrd\n1'b0: fsyniqdiven决定，或者fsyniqdiven信号的1us延迟", "Name:fsynvcoen\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO使能控制\n1'b0: disable\n1'b1: enable", '0x00']
PAGE1_NAME88 = ["Name:txpapoutcrnt\nAccess: RW\nReset Value: 4'h7\nDescription:\n模拟模块内没有接", "Name:txpapoutcrnt\nAccess: RW\nReset Value: 4'h7\nDescription:\n模拟模块内没有接", "Name:txpapoutcrnt\nAccess: RW\nReset Value: 4'h7\nDescription:\n模拟模块内没有接", "Name:txpapoutcrnt\nAccess: RW\nReset Value: 4'h7\nDescription:\n模拟模块内没有接", "Name:txpaipolyprb\nAccess: RW\nReset Value: 1'b0\nDescription:\n滤波器偏置电流减半", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME87 = ["Name:fsynsdnint_ovrd\nAccess: RW\nReset Value: 6'h1e\nDescription:\nSDM整数寄存器输入", "Name:fsynsdnint_ovrd\nAccess: RW\nReset Value: 6'h1e\nDescription:\nSDM整数寄存器输入", "Name:fsynsdnint_ovrd\nAccess: RW\nReset Value: 6'h1e\nDescription:\nSDM整数寄存器输入", "Name:fsynsdnint_ovrd\nAccess: RW\nReset Value: 6'h1e\nDescription:\nSDM整数寄存器输入", "Name:fsynsdnint_ovrd\nAccess: RW\nReset Value: 6'h1e\nDescription:\nSDM整数寄存器输入", "Name:fsynsdnint_ovrd\nAccess: RW\nReset Value: 6'h1e\nDescription:\nSDM整数寄存器输入", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsynsdnint_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nSDM整数输入寄存器控制位：\n1'b0: SDM整数部分由频率计算输入\n1‘b1: SDM整数部分由[5:0]输入", '0x00']
PAGE1_NAME86 = ["Name:fsynsdordr\nAccess: RW\nReset Value: 2'h1\nDescription:\nSDM模块的运行阶数\nfsynsdordr[0] : i_sd_3rd\nfsynsdordr[1] : i_sd_4th", "Name:fsynsdordr\nAccess: RW\nReset Value: 2'h1\nDescription:\nSDM模块的运行阶数\nfsynsdordr[0] : i_sd_3rd\nfsynsdordr[1] : i_sd_4th", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:fsynsden\nAccess: RW\nReset Value: 1'b0\nDescription:\n复位SDM模块，虽然是en信号，但是是接到复位的\n1'b1: not reset\n1'b0: reset", '0x00']
PAGE1_NAME85 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsyniqdivtx_rst_n_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nTX模式下IQ 2分频器复位使能\n1‘b0: enable\n1'b1: disable", "Name:fsyniqdivtx_rst_n_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\ndigctrl_fsyniqdivtxrstn信号(数字到模拟)的选择\n1’b1: 采用fsyniqdivtx_rst_n_ovrd的值\n1‘b0: 系统软件复位(0x80)，或者通过置端口SYBYEN引脚为1，使IP由sleep模式进入standby模式，从而复位iq tx，其它时候由fsyniqdivtxmode决定复位情况", "Name:fsynlfdac\nAccess: RW\nReset Value: 4'b1001\nDescription:\n直接连到Loop Filter的DAC数字输入端\n300mV~780mV\r\nLSB=32mV", "Name:fsynlfdac\nAccess: RW\nReset Value: 4'b1001\nDescription:\n直接连到Loop Filter的DAC数字输入端\n300mV~780mV\r\nLSB=32mV", "Name:fsynlfdac\nAccess: RW\nReset Value: 4'b1001\nDescription:\n直接连到Loop Filter的DAC数字输入端\n300mV~780mV\r\nLSB=32mV", "Name:fsynlfdac\nAccess: RW\nReset Value: 4'b1001\nDescription:\n直接连到Loop Filter的DAC数字输入端\n300mV~780mV\r\nLSB=32mV", "Name:fsynlfdacen\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL Calibration时LF的DAC使能控制信号。\n当VCO Calibration Enable无效时，此位控制Loop Filter里面的dacen；当VCO Calibration Enable = 1时，此位无效", '0x00']
PAGE1_NAME84 = ["Name:fsyniqdivpaouten\nAccess: RW\nReset Value: 1'b0\nDescription:\nIQ 2分频器给PA的输入信号使能\n1'b1: enable\n1’b0: disable", "Name:fsyniqdivibiasp\nAccess: RW\nReset Value: 3'h4\nDescription:\nIQ 2分频器偏置电流调节控制P型", "Name:fsyniqdivibiasp\nAccess: RW\nReset Value: 3'h4\nDescription:\nIQ 2分频器偏置电流调节控制P型", "Name:fsyniqdivibiasp\nAccess: RW\nReset Value: 3'h4\nDescription:\nIQ 2分频器偏置电流调节控制P型", "Name:fsyniqdiven_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nIQ 2分频器使能控制\n1'b1: enable\n1'b0: disable", "Name:fsyniqdiven_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyniqdiven信号(数字到模拟)的选择\n1‘b1: fsyniqdiven_ovrd\n1'b0: fsyniqdiven，或者fsyniqdiven信号的1us延迟", "Name:fsyniqdiven\nAccess: RW\nReset Value: 1'b0\nDescription:\nIQ 2分频器使能控制\n1'b1: enable\n1'b0: disable", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME83 = ["Name:fsynchpiofst\nAccess: RW\nReset Value: 4'h2\nDescription:\n当PLL BW Calibration无效时，\nCP Offset current 大小由此位控制", "Name:fsynchpiofst\nAccess: RW\nReset Value: 4'h2\nDescription:\n当PLL BW Calibration无效时，\nCP Offset current 大小由此位控制", "Name:fsynchpiofst\nAccess: RW\nReset Value: 4'h2\nDescription:\n当PLL BW Calibration无效时，\nCP Offset current 大小由此位控制", "Name:fsynchpiofst\nAccess: RW\nReset Value: 4'h2\nDescription:\n当PLL BW Calibration无效时，\nCP Offset current 大小由此位控制", "Name:fsynchpiofst_cal\nAccess: RW\nReset Value: 4'b0\nDescription:\n当PLL BW Calibration有效，且up/down mode（由寄存器r26[7:6]配置）不为0时，CP Offset current 大小由此位控制", "Name:fsynchpiofst_cal\nAccess: RW\nReset Value: 4'b0\nDescription:\n当PLL BW Calibration有效，且up/down mode（由寄存器r26[7:6]配置）不为0时，CP Offset current 大小由此位控制", "Name:fsynchpiofst_cal\nAccess: RW\nReset Value: 4'b0\nDescription:\n当PLL BW Calibration有效，且up/down mode（由寄存器r26[7:6]配置）不为0时，CP Offset current 大小由此位控制", "Name:fsynchpiofst_cal\nAccess: RW\nReset Value: 4'b0\nDescription:\n当PLL BW Calibration有效，且up/down mode（由寄存器r26[7:6]配置）不为0时，CP Offset current 大小由此位控制", '0x00']
PAGE1_NAME82 = ["Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:fsynpfdpol\nAccess: RW\nReset Value: 1'b0\nDescription:\nPFD极性控制\n1'b0: refclk leading\n1’b1: feedback leading", "Name:fsynchpampen\nAccess: RW\nReset Value: 1'b1\nDescription:\nCP内部运放使能\n1‘b1: enable\r\n1'b0: disable", "Name:fsynchpen\nAccess: RW\nReset Value: 1'b0\nDescription:\nPFD CP使能\n1‘b1: enable\n1'b0: disable", '0x00']
PAGE1_NAME81 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsynfbdiv_rst_n_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n多模分频复位信号\n1‘b0: enable\n1'b1: disable", "Name:fsynfbdiv_rst_n_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\n多模分频复位信号选择(数字到模拟)\n1’b1: fsynfbdiv_rst_n_ovrd\n1'b0: 系统软件复位(0x80)，或者通过置端口SYBYEN引脚为1，使IP由sleep模式进入standby模式，从而复位多模分频，当fsynfbdiven为1时开始计时，满足一个16MHz周期后该复位释放", "Name:fsynfbdiven\nAccess: RW\nReset Value: 1'b0\nDescription:\n多模分频使能\n1‘b1: enable\n1'b0: disable", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsyncaldiv_rst_n_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL calibration模式复位", "Name:fsyncaldiv_rst_n_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL calibration模式复位选择信号\n1'b1: fsyncaldiv_rst_n_ovrd\n1'b0: 系统软件复位(0x80)，或者通过置端口SYBYEN引脚为1，使IP由sleep模式进入standby模式，从而复位PLL calibration的分频，当fsyncaldiven为1时开始计时，满足10us后该复位释放", "Name:fsyncaldiven\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL calibration模式使能控制\nfsyn(PLL) calibration dividor enable\n1'b0: not enabled\n1'b1: enabled", '0x00']
PAGE1_NAME80 = ["Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_calwrd_2\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 2MHz mode", "Name:rxfltnr_caldone_2\nAccess: RO\nReset Value: 1'b0\nDescription:\nAn outpu flag to show that the RX filter auto-tuning process has been finished successfully for 2MHz mode\n1'b0: Calibration process in progress\n1'b1: Calibration process done successfully", '0x00']
PAGE1_NAME79 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:rxofstcmpen\nAccess: RW\nReset Value: 1'b0\nDescription:\nrx dc offset比较器使能信号", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:txpadly\nAccess: RW\nReset Value: 2'b1\nDescription:\ntx pa power-up delay time\n2'b00：100ns\n2'b01：200ns\n2'b10：300ns\r\n2'b11：400ns", "Name:txpadly\nAccess: RW\nReset Value: 2'b1\nDescription:\ntx pa power-up delay time\n2'b00：100ns\n2'b01：200ns\n2'b10：300ns\r\n2'b11：400ns", "Name:ext_gain_idx_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n外部gain输入使能\n1'b1：使用外部输入gain\n1'b0：使用计算的gain", '0x00']
PAGE1_NAME78 = ["Name:rxmxrvcmipolyprb\nAccess: RW\nReset Value: 1'b0\nDescription:\nLO的共模电平模块使能信号\n1'b0: 有效，共模电平\n1’b1: 衬底电压为0", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:rxmxrbulk\nAccess: RW\nReset Value: 3'h3\nDescription:\nMixer开关管的衬底电压控制\n111: 422mV  110: 378mV\n101: 332mV  100: 287mV\n011: 241mV  010: 194mV\n001: 147mV  000: 100mV", "Name:rxmxrbulk\nAccess: RW\nReset Value: 3'h3\nDescription:\nMixer开关管的衬底电压控制\n111: 422mV  110: 378mV\n101: 332mV  100: 287mV\n011: 241mV  010: 194mV\n001: 147mV  000: 100mV", "Name:rxmxrbulk\nAccess: RW\nReset Value: 3'h3\nDescription:\nMixer开关管的衬底电压控制\n111: 422mV  110: 378mV\n101: 332mV  100: 287mV\n011: 241mV  010: 194mV\n001: 147mV  000: 100mV", "Name:rxmxrblkipolyprb\nAccess: RW\nReset Value: 1'b0\nDescription:\nMixer开关管的衬底电压使能信号\n1'b0: 有效，衬底电压200-300mV\n1'b1: 衬底电压为零", '0x00']
PAGE1_NAME77 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME76 = ["Name:tninj_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX Filter Tuner Tone Inject信号强制寄存器输出选择\n1'b0: 用FSM控制输出\n1'b1: 从寄存器[1]输出", "Name:tninj_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX Filter Tuner Tone Inject信号强制寄存器输出配置\n当[0]=1时，Tone inject信号由此位寄存器配置", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME75 = ["Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcalwrd\nAccess: RW\nReset Value: 7'h1C\nDescription:\nRX Filter Tuner强制输出校准值配置\n当[7]=1时，输出此寄存器的值用作校准值", "Name:rxfltnrovrdcal\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX Filter Tuner强制输出校准值控制\n1'b0: 输出校准值\n1'b1: 输出[6:0]的寄存器配置值", '0x00']
PAGE1_NAME74 = ["Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", "Name:rxfltnr_nterm\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n搜索结束时周期计数器的值，不一定是最后一轮，\n而是最小误差对应的计数器值", '0x00']
PAGE1_NAME73 = ["Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", "Name:rxfltnr_ninit\nAccess: RO\nReset Value: 8'b0\nDescription:\nRX Filter Tuner相关\n最开始搜索时周期计数器的值", '0x00']
PAGE1_NAME72 = ["Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:rxfltnr_nmin_1\nAccess: RW\nReset Value: 7'h2D\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin1", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME71 = ["Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:rxfltnr_nmax_1\nAccess: RW\nReset Value: 7'h5A\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax1", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME70 = ["Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 8'b0\nDescription:\n", '0x00']
PAGE1_NAME69 = ["Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:rxfltnr_toneinj\nAccess: RO\nReset Value: 1'b1\nDescription:\n滤波器rc tuner数字激励控制信号", '0x00']
PAGE1_NAME68 = ["Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_calwrd_1\nAccess: RO\nReset Value: 7'h40\nDescription:\nThe output result of the RX filter auto-tuning process for the 1MHz mode", "Name:rxfltnr_caldone_1\nAccess: RO\nReset Value: 1'b0\nDescription:\nAn output flag to show that the RX filter auto-tuning process has been finished successfully for 1MHz mode\n1'b0: Calibration process in progress\n1'b1: Calibration process done successfully", '0x00']
PAGE1_NAME67 = ["Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:rxfltnrcaladj\nAccess: RW\nReset Value: 7'b0\nDescription:\nRX Filter Tuner校准值的额外偏移量，有符号数\n用于对校正值进行额外的偏移", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME66 = ["Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:rxfltnrtune\nAccess: RW\nReset Value: 1'b0\nDescription:\nRx filter tuner start signal\n1'b0: Calibration not started\n1'b1: Calibration started", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:rxfltnren\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX Filter Tuner Enable，经过Power Group的bit[18]控制\n当RX DC Offset Cancel Calibration有效或者RX Filter Offset Calibration有效时，数字射频接口的rxfltnren有效，否则由此寄存器决定。", '0x00']
PAGE1_NAME65 = ["Name:fsynbwcalndivfix\nAccess: RW\nReset Value: 3'h7\nDescription:\nPLL BW Calibration校验结果偏移，可以用于增减一个\n固定的偏移值", "Name:fsynbwcalndivfix\nAccess: RW\nReset Value: 3'h7\nDescription:\nPLL BW Calibration校验结果偏移，可以用于增减一个\n固定的偏移值", "Name:fsynbwcalndivfix\nAccess: RW\nReset Value: 3'h7\nDescription:\nPLL BW Calibration校验结果偏移，可以用于增减一个\n固定的偏移值", "Name:fsynbwcalen_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n手动控制PLL BW Calibration使能", "Name:fsynbwcalen_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\n手动PLL BW Calibration选择\n1'b0: 不进行手动选择\n1'b1: 由bit[3]决定是否手动控制", "Name:fsynbwcalfrcupdn\nAccess: RW\nReset Value: 2'b0\nDescription:\nPFD输出外部控制（可以内部选通PFD自己比较后输出或者外灌信号输出）\n00 01 10 11表示PFD四种比较状态，可以由外部输入", "Name:fsynbwcalfrcupdn\nAccess: RW\nReset Value: 2'b0\nDescription:\nPFD输出外部控制（可以内部选通PFD自己比较后输出或者外灌信号输出）\n00 01 10 11表示PFD四种比较状态，可以由外部输入", "Name:fsynbwcalfrcupdnovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPFD up/down单独做一次的debug模式，up/down控制由fsynbwcalfrcupdn[1:0]决定，且电流控制字为已校准的值。\n1’b0: 不由寄存器强制控制\n1‘b1: 由[6:5]强制控制", '0x00']
PAGE1_NAME64 = ["Name:fsynbwcal_chpioutch\nAccess: RO\nReset Value: 6'h20\nDescription:\nHolds the calibration word associated with the group indexed by fsynbwcal_index\nPLL BW multi-channel Calibration的时候，每个分组的结果存放在一个寄存器堆里面，这个寄存器用于给定分组号后读出结果。具体操作参见0x3E[3:0]的说明。", "Name:fsynbwcal_chpioutch\nAccess: RO\nReset Value: 6'h20\nDescription:\nHolds the calibration word associated with the group indexed by fsynbwcal_index\nPLL BW multi-channel Calibration的时候，每个分组的结果存放在一个寄存器堆里面，这个寄存器用于给定分组号后读出结果。具体操作参见0x3E[3:0]的说明。", "Name:fsynbwcal_chpioutch\nAccess: RO\nReset Value: 6'h20\nDescription:\nHolds the calibration word associated with the group indexed by fsynbwcal_index\nPLL BW multi-channel Calibration的时候，每个分组的结果存放在一个寄存器堆里面，这个寄存器用于给定分组号后读出结果。具体操作参见0x3E[3:0]的说明。", "Name:fsynbwcal_chpioutch\nAccess: RO\nReset Value: 6'h20\nDescription:\nHolds the calibration word associated with the group indexed by fsynbwcal_index\nPLL BW multi-channel Calibration的时候，每个分组的结果存放在一个寄存器堆里面，这个寄存器用于给定分组号后读出结果。具体操作参见0x3E[3:0]的说明。", "Name:fsynbwcal_chpioutch\nAccess: RO\nReset Value: 6'h20\nDescription:\nHolds the calibration word associated with the group indexed by fsynbwcal_index\nPLL BW multi-channel Calibration的时候，每个分组的结果存放在一个寄存器堆里面，这个寄存器用于给定分组号后读出结果。具体操作参见0x3E[3:0]的说明。", "Name:fsynbwcal_chpioutch\nAccess: RO\nReset Value: 6'h20\nDescription:\nHolds the calibration word associated with the group indexed by fsynbwcal_index\nPLL BW multi-channel Calibration的时候，每个分组的结果存放在一个寄存器堆里面，这个寄存器用于给定分组号后读出结果。具体操作参见0x3E[3:0]的说明。", "Name:lotesten\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL LO输出测试使能", "Name:rxobufen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX滤波器输出测试使能", '0x00']
PAGE1_NAME63 = ["Name:fsynbwcal_index\nAccess: RW\nReset Value: 4'b0\nDescription:\n多通道PLL BW校正强制寄存器输出的地址配置；共有16组寄存器需要预先完成配置，其时序是：\n1. 配置0x3B[7]=0\n2. 将值写入0x3A[5:0]\n2. 将地址(0~15)写入此域\n3. 配置0x3B[7]=1，然后配置0x3B[7]=0，完成一个寄存器的写入\n\n除此以外，不在校正工作期间，此域用于读出校正结果的地址选择，其时序时：\n1. 将地址(0~15)写入此域\n2. 从0x3F[5:0]读出对应分组的校正结果", "Name:fsynbwcal_index\nAccess: RW\nReset Value: 4'b0\nDescription:\n多通道PLL BW校正强制寄存器输出的地址配置；共有16组寄存器需要预先完成配置，其时序是：\n1. 配置0x3B[7]=0\n2. 将值写入0x3A[5:0]\n2. 将地址(0~15)写入此域\n3. 配置0x3B[7]=1，然后配置0x3B[7]=0，完成一个寄存器的写入\n\n除此以外，不在校正工作期间，此域用于读出校正结果的地址选择，其时序时：\n1. 将地址(0~15)写入此域\n2. 从0x3F[5:0]读出对应分组的校正结果", "Name:fsynbwcal_index\nAccess: RW\nReset Value: 4'b0\nDescription:\n多通道PLL BW校正强制寄存器输出的地址配置；共有16组寄存器需要预先完成配置，其时序是：\n1. 配置0x3B[7]=0\n2. 将值写入0x3A[5:0]\n2. 将地址(0~15)写入此域\n3. 配置0x3B[7]=1，然后配置0x3B[7]=0，完成一个寄存器的写入\n\n除此以外，不在校正工作期间，此域用于读出校正结果的地址选择，其时序时：\n1. 将地址(0~15)写入此域\n2. 从0x3F[5:0]读出对应分组的校正结果", "Name:fsynbwcal_index\nAccess: RW\nReset Value: 4'b0\nDescription:\n多通道PLL BW校正强制寄存器输出的地址配置；共有16组寄存器需要预先完成配置，其时序是：\n1. 配置0x3B[7]=0\n2. 将值写入0x3A[5:0]\n2. 将地址(0~15)写入此域\n3. 配置0x3B[7]=1，然后配置0x3B[7]=0，完成一个寄存器的写入\n\n除此以外，不在校正工作期间，此域用于读出校正结果的地址选择，其时序时：\n1. 将地址(0~15)写入此域\n2. 从0x3F[5:0]读出对应分组的校正结果", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", 'Name:fsynbwcalmode_ovrd\nAccess: RW\nReset Value: 1‘b1\nDescription:\nPLL BW Calibration模式寄存器配置，\n仅在r003e[7]=1时有效\n\n1‘b0: 多次逼近模式\n1’b1: 单次除法模式', "Name:fsynbwcalmode_sel\nAccess: RW\nReset Value: 1'b1\nDescription:\nPLL BW Calibration模式寄存器配置选择\n1'b0: 根据r0003[7]选择模式，选择single则模式为0；选择multi-channel则模式为1\n1’b1: 使用fsynbwcalmode_ovrd进行配置\n\n模式的定义为：\n1‘b0: 多次逼近模式\n1’b1: 单次除法模式\n", '0x00']
PAGE1_NAME62 = ["Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[7:0]\nAccess: RW\nReset Value: 8'he0\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", '0x00']
PAGE1_NAME61 = ["Name:fsynbwlcal_lock_v[10:8]\nAccess: RW\nReset Value: 3'h1\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[10:8]\nAccess: RW\nReset Value: 3'h1\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:fsynbwlcal_lock_v[10:8]\nAccess: RW\nReset Value: 3'h1\nDescription:\nPLL BW Calibration时，每次切换Channel或者分组时，\n让CaldivClk先进行这个数量的计数（默认值在300M时钟下约30us），稳定后再启动状态机。", "Name:NA\nAccess: RO\nReset Value: 5'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'h0\nDescription:\n", '0x00']
PAGE1_NAME60 = ["Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:fsynbwcalchpioutenovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n多通道PLL BW校正寄存器输出的写使能位，具体操作见0x3E[3:0]的介绍", '0x00']
PAGE1_NAME59 = ["Name:fsynbwcalchpioutovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\n多通道PLL BW校正寄存器输出的写入位，具体操作见0x3E[3:0]的介绍", "Name:fsynbwcalchpioutovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\n多通道PLL BW校正寄存器输出的写入位，具体操作见0x3E[3:0]的介绍", "Name:fsynbwcalchpioutovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\n多通道PLL BW校正寄存器输出的写入位，具体操作见0x3E[3:0]的介绍", "Name:fsynbwcalchpioutovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\n多通道PLL BW校正寄存器输出的写入位，具体操作见0x3E[3:0]的介绍", "Name:fsynbwcalchpioutovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\n多通道PLL BW校正寄存器输出的写入位，具体操作见0x3E[3:0]的介绍", "Name:fsynbwcalchpioutovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\n多通道PLL BW校正寄存器输出的写入位，具体操作见0x3E[3:0]的介绍", "Name:fsynbwcalmchovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL bandwidth calibration多通道校正时强制寄存器输出控制\n1'b0: 不强制输出\n1’b1: 多通道强制从寄存器堆输出校正值。\n多通道PLL BW校正的时候会分成16组，使用的时候根据channel index选择不同的分组。强制输出的16组校正值，通过操作0x3E寄存器写入。", "Name:fsynbwcalovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL BW校正结果强制输出控制\n1‘b0: 不强制输出\n1'b1: PLL BW校正结果总是从[5:0]输出", '0x00']
PAGE1_NAME58 = ["Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", "Name:fsynbwcal_minerr\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration时，用于读出逼近模式下的最小误差", '0x00']
PAGE1_NAME57 = ["Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", '0x00']
PAGE1_NAME56 = ["Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[15:8]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", '0x00']
PAGE1_NAME55 = ["Name:fsynbwcal_freqdbg[19:16]\nAccess: RO\nReset Value: 4'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[19:16]\nAccess: RO\nReset Value: 4'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[19:16]\nAccess: RO\nReset Value: 4'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:fsynbwcal_freqdbg[19:16]\nAccess: RO\nReset Value: 4'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出单次计数值，用于Debug时计算单次VCO频率。", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME54 = ["Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", '0x00']
PAGE1_NAME53 = ["Name:fsynbwcal_dfout[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:fsynbwcal_dfout[9:8]\nAccess: RO\nReset Value: 2'b0\nDescription:\nPLL BW Calibration Debug模式下在此域读出up/down\n两次频率测量的计数差值，反映up/down两次频差，但需要结合计数长度，计算精度等才能计算得出真正VCO频差。", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME52 = ["Name:fsynbwcal_chpiout\nAccess: RO\nReset Value: 6'h1F\nDescription:\n可读出实时数模端口上的chpiout[5:0]信号", "Name:fsynbwcal_chpiout\nAccess: RO\nReset Value: 6'h1F\nDescription:\n可读出实时数模端口上的chpiout[5:0]信号", "Name:fsynbwcal_chpiout\nAccess: RO\nReset Value: 6'h1F\nDescription:\n可读出实时数模端口上的chpiout[5:0]信号", "Name:fsynbwcal_chpiout\nAccess: RO\nReset Value: 6'h1F\nDescription:\n可读出实时数模端口上的chpiout[5:0]信号", "Name:fsynbwcal_chpiout\nAccess: RO\nReset Value: 6'h1F\nDescription:\n可读出实时数模端口上的chpiout[5:0]信号", "Name:fsynbwcal_chpiout\nAccess: RO\nReset Value: 6'h1F\nDescription:\n可读出实时数模端口上的chpiout[5:0]信号", "Name:fsynbwcal_caldone\nAccess: RO\nReset Value: 1'b0\nDescription:\nPLL BW multi-channel calibration过程中每一次\nchannel完成标记，用途可忽略。", "Name:fsynbwcal_mchcaldone\nAccess: RO\nReset Value: 1'b0\nDescription:\nPLL BW calibration done flag. It's asserted after calibrating the 16 groups\n当完成整个Calibration时，此位置1；当开始新的Calibration时，此位自动清0。软件可以通过轮询此位等待mult-channel PLL BW calibration的完成。", '0x00']
PAGE1_NAME51 = ["Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'h00\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", '0x00']
PAGE1_NAME50 = ["Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", "Name:fsynbwcalmaxcnt\nAccess: RW\nReset Value: 8'hab\nDescription:\nPLL BW Calibration测量时长定义\n此寄存器，定义的是约37.5MHz（VCO的128分频，由r002d[4:3]配置）的被测时钟，以25kHz的除法进度测量误差（由r002d[2:0]配置）下的计数周期个数。\n最终得到单次频率测量的时间，为16M参考时钟的\nfsynbwcalmaxcnt >> (accuracy + 3 - ratio)\n个周期", '0x00']
PAGE1_NAME49 = ["Name:fsynbwcal_powerup\nAccess: RW\nReset Value: 4'b1\nDescription:\nPLL BW Calibration Power Up Time\n上电稳定时间，单位为us", "Name:fsynbwcal_powerup\nAccess: RW\nReset Value: 4'b1\nDescription:\nPLL BW Calibration Power Up Time\n上电稳定时间，单位为us", "Name:fsynbwcal_powerup\nAccess: RW\nReset Value: 4'b1\nDescription:\nPLL BW Calibration Power Up Time\n上电稳定时间，单位为us", "Name:fsynbwcal_powerup\nAccess: RW\nReset Value: 4'b1\nDescription:\nPLL BW Calibration Power Up Time\n上电稳定时间，单位为us", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME48 = ["Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[7:0]\nAccess: RW\nReset Value: 8'h1F\nDescription:\nPLL BW Calibration理想参考频差", '0x00']
PAGE1_NAME47 = ["Name:fsynbwcaldfcmp[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nPLL BW Calibration理想参考频差", "Name:fsynbwcaldfcmp[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nPLL BW Calibration理想参考频差", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME46 = ["Name:fsynbwcalaccrcy[2:0]\nAccess: RW\nReset Value: 3'h4\nDescription:\nPLL BW Calibration频差测量精度\n3'd0: 25KHz\n3'd1: 50kHz \n3'd2: 100kHz\n3'd3: 200KHz\n其他: 400kHz ", "Name:fsynbwcalaccrcy[2:0]\nAccess: RW\nReset Value: 3'h4\nDescription:\nPLL BW Calibration频差测量精度\n3'd0: 25KHz\n3'd1: 50kHz \n3'd2: 100kHz\n3'd3: 200KHz\n其他: 400kHz ", "Name:fsynbwcalaccrcy[2:0]\nAccess: RW\nReset Value: 3'h4\nDescription:\nPLL BW Calibration频差测量精度\n3'd0: 25KHz\n3'd1: 50kHz \n3'd2: 100kHz\n3'd3: 200KHz\n其他: 400kHz ", "Name:fsyncaldivcalclk[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nPLL Calibration dividor分频比例\n00: VCO div clock = ~300MHz（即2.4G的8分频）\n01: VCO div clock = ~150MHz（即2.4G的16分频）\n10: VCO div clock = ~75MHz（即2.4G的32分频）\n11: VCO div clock = ~37.5MHz（即2.4G的64分频）\n直接连接到RF的phy_digctrl_fsyncaldivcalclk[1:0]", "Name:fsyncaldivcalclk[1:0]\nAccess: RW\nReset Value: 2'h0\nDescription:\nPLL Calibration dividor分频比例\n00: VCO div clock = ~300MHz（即2.4G的8分频）\n01: VCO div clock = ~150MHz（即2.4G的16分频）\n10: VCO div clock = ~75MHz（即2.4G的32分频）\n11: VCO div clock = ~37.5MHz（即2.4G的64分频）\n直接连接到RF的phy_digctrl_fsyncaldivcalclk[1:0]", "Name:fsynbwcalstartmchcal\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL BW multi-channel start calibration control. It starts the calibration of the 16 groups.\n开始BW Calibration的时候置1，完成全部BW Calibration后清0.", "Name:fsynbwcalanaen_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsynbwcalanaen强制寄存器控制选择信号\n1‘b0: fsynbwcalanaen由Calibration状态机控制，在一个通道的Calibration的过程里此位为1\n1'b1: 采用0x2D的fsynbwcalanaen_ovrd(bit7)作为fsynbwcalanaen", "Name:fsynbwcalanaen_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsynbwcalanaen的改写值", '0x00']
PAGE1_NAME45 = ["Name:soft_th0\nAccess: RW\nReset Value: 6'hA\nDescription:\n解调器相关的一个门限值", "Name:soft_th0\nAccess: RW\nReset Value: 6'hA\nDescription:\n解调器相关的一个门限值", "Name:soft_th0\nAccess: RW\nReset Value: 6'hA\nDescription:\n解调器相关的一个门限值", "Name:soft_th0\nAccess: RW\nReset Value: 6'hA\nDescription:\n解调器相关的一个门限值", "Name:soft_th0\nAccess: RW\nReset Value: 6'hA\nDescription:\n解调器相关的一个门限值", "Name:soft_th0\nAccess: RW\nReset Value: 6'hA\nDescription:\n解调器相关的一个门限值", "Name:iq_swap\nAccess: RW\nReset Value: 1'b0\nDescription:\n解调器IQ交换使能配置\n1’b0: IQ不交换\n1‘b1: IQ交换", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME44 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:ed_mode\nAccess: RW\nReset Value: 1'b0\nDescription:\nenergy detection\n1'b0: ED activated in zigbee RX\n1'b1: ED workingin zigbee scan mode", "Name:rssi_read\nAccess: RW\nReset Value: 1'b0\nDescription:\nenable required digital RX chain modules to allow upper layer to monitor the RSSI reading", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME43 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:iq_scale_gain_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n解调器中的IQ scale增益强制寄存器配置控制\n1‘b0: 不强制使用寄存器增益控制\n1'b1: 使用[7:4]强制改写", "Name:demod_iq_scale_gain_rgn_ovrd\nAccess: RW\nReset Value: 4'b2\nDescription:\n解调器中IQ Scale增益强制改写配置", "Name:demod_iq_scale_gain_rgn_ovrd\nAccess: RW\nReset Value: 4'b2\nDescription:\n解调器中IQ Scale增益强制改写配置", "Name:demod_iq_scale_gain_rgn_ovrd\nAccess: RW\nReset Value: 4'b2\nDescription:\n解调器中IQ Scale增益强制改写配置", "Name:demod_iq_scale_gain_rgn_ovrd\nAccess: RW\nReset Value: 4'b2\nDescription:\n解调器中IQ Scale增益强制改写配置", '0x00']
PAGE1_NAME42 = ["Name:rxfltrtnr_open_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrxfltrtnr_open信号强制寄存器配置选择\n1‘b0: 由RX Filter Tuner逻辑输出\n1’b1: 由r0029[1]配置输出", "Name:rxfltrtnr_open_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX Filter Tuner Open寄存器配置\n滤波器rc tuner断开数字信号", "Name:rxfltroutgnd\nAccess: RW\nReset Value: 1'b0\nDescription:\n滤波器关断时，滤波器输出零电平使能\n1‘b0: 滤波器输出共模电平\n1’b1: 滤波器输出零电平", "Name:rxfltripolyprb\nAccess: RW\nReset Value: 1'b0\nDescription:\n滤波器偏置电流减半控制信号\n1‘b0: 偏置电流5uA\n1’b1: 偏置电流2.5uA", "Name:rxfltrif\nAccess: RW\nReset Value: 2'b11\nDescription:\nRX Filter中心频点的选择\n【TODO】\n10: 1MHz中频\r\n11: 2MHz中频", "Name:rxfltrif\nAccess: RW\nReset Value: 2'b11\nDescription:\nRX Filter中心频点的选择\n【TODO】\n10: 1MHz中频\r\n11: 2MHz中频", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\nReserved", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\nReserved", '0x00']
PAGE1_NAME41 = ["Name:rfmtchntwkpaen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRF匹配网络PA控制，经过Power Group控制位bit[15]控制\n当TX enable时，数字射频接口上的rfmtchntwkpaen=1；其他时候，由此寄存器控制。", "Name:rfmtchntwklnaen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRF匹配网络LNA控制，经过Power Group控制位bit[14]控制\n当RX enable或者RX DC offset calibration enable时，数字射频接口上的rfmtchntwklnaen=1；其他时候，由此寄存器控制。", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME40 = ["Name:fsynbwcal_dn_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP N电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_dn_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP N电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_dn_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP N电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_dn_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP N电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_dn_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP N电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_dn_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP N电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_dn_ovrd_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nCP N电流是否采用寄存器数值\n1'b0: CP N电流不从寄存器r0027[5:0]取值，根据Calibration状态或者正常工作状态决定输出；\n1’b1: CP N电流从寄存器r0027[5:0]取值", "Name:fsynbwcal_up_ovrd_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nCP P电流是否采用寄存器数值\n1'b0: CP P电流不从寄存器r0026[5:0]取值，根据Calibration状态或者正常工作状态决定输出；\n1’b1: CP P电流从寄存器r0026[5:0]取值", '0x00']
PAGE1_NAME39 = ["Name:fsynbwcal_up_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP P电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_up_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP P电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_up_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP P电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_up_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP P电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_up_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP P电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_up_ovrdwd\nAccess: RW\nReset Value: 6'b0\nDescription:\nCP P电流大小选择控制位\n10uA~49uA    step:0.625nA", "Name:fsynbwcal_updn_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\nPLL BW Calibration up/down模式选择\n2‘b00: 此模式下，BW Calibration同一次测量的前后两个状态为frcupdn=10，和frcupdn=01；且ioffset[3:0]固定为r0052[7:4]的配置值，默认为4'd0\n2'b01: 此模式下，BW Calibration同一次测量的前后两个状态为frcupdn=00，和frcupdn=01；且ioffset[3:0]固定为r0052[7:4]的配置值，默认为4'd0\n2'b10: 此模式下，BW Calibration同一次测量的前后两个状态为frcupdn=00，和frcupdn=01；且ioffset[3:0]固定为r0025[3:0]的配置值，默认为4'd9\n2'b11: Reserved，实际同2'b00", "Name:fsynbwcal_updn_mode\nAccess: RW\nReset Value: 2'b0\nDescription:\nPLL BW Calibration up/down模式选择\n2‘b00: 此模式下，BW Calibration同一次测量的前后两个状态为frcupdn=10，和frcupdn=01；且ioffset[3:0]固定为r0052[7:4]的配置值，默认为4'd0\n2'b01: 此模式下，BW Calibration同一次测量的前后两个状态为frcupdn=00，和frcupdn=01；且ioffset[3:0]固定为r0052[7:4]的配置值，默认为4'd0\n2'b10: 此模式下，BW Calibration同一次测量的前后两个状态为frcupdn=00，和frcupdn=01；且ioffset[3:0]固定为r0025[3:0]的配置值，默认为4'd9\n2'b11: Reserved，实际同2'b00", '0x00']
PAGE1_NAME38 = ["Name:fsynchpiofst_2\nAccess: RW\nReset Value: 4'h9\nDescription:\nPLL BW Calibration up/down mode=2’b10的时候，\n用此位来控制ioffset[3:0]接口", "Name:fsynchpiofst_2\nAccess: RW\nReset Value: 4'h9\nDescription:\nPLL BW Calibration up/down mode=2’b10的时候，\n用此位来控制ioffset[3:0]接口", "Name:fsynchpiofst_2\nAccess: RW\nReset Value: 4'h9\nDescription:\nPLL BW Calibration up/down mode=2’b10的时候，\n用此位来控制ioffset[3:0]接口", "Name:fsynchpiofst_2\nAccess: RW\nReset Value: 4'h9\nDescription:\nPLL BW Calibration up/down mode=2’b10的时候，\n用此位来控制ioffset[3:0]接口", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME37 = ["Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_2m\nAccess: RW\nReset Value: 8'h73\nDescription:\nRX Filter Tuner 2MHz自激波形八个周期的宽度的理想值，单位时时钟周期", '0x00']
PAGE1_NAME36 = ["Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", "Name:rxfltnr_refcycles_1m\nAccess: RW\nReset Value: 8'h78\nDescription:\nRX Filter Tuner 1MHz自激波形四个周期的宽度的理想值，单位时时钟周期", '0x00']
PAGE1_NAME35 = ["Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:rxfltnr_nmin_2\nAccess: RW\nReset Value: 8'h16\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmin2", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME34 = ["Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:rxfltnr_nmax_2\nAccess: RW\nReset Value: 8'h2d\nDescription:\nRX Filter Tuner输出到模拟的校准结果需要先经过一个映射，这是其中一个映射参数nmax2", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME33 = ['Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', 'Name:fsyncvoatmxcnt[7:0]\nAccess: RW\nReset Value: 8‘h4f\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。', '0x00']
PAGE1_NAME32 = ["Name:fsyncvoatmxcnt[8]\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Tuner Max Counter\n用于VCO Tuner模块，Calibration的时候用于计时的参考时钟的个数，可以认为在fsynvcoatmxcnt[8:0]个参考时钟的时间内，计算fdiv的个数，来判断VCO的频率。", "Name:error_limit[3:0]\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO Tuner时的预设误差\n当测量时超过此值，意味着VCO频率已经超过预期，此时可以提前结束本轮测量", "Name:error_limit[3:0]\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO Tuner时的预设误差\n当测量时超过此值，意味着VCO频率已经超过预期，此时可以提前结束本轮测量", "Name:error_limit[3:0]\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO Tuner时的预设误差\n当测量时超过此值，意味着VCO频率已经超过预期，此时可以提前结束本轮测量", "Name:error_limit[3:0]\nAccess: RW\nReset Value: 4'h8\nDescription:\nVCO Tuner时的预设误差\n当测量时超过此值，意味着VCO频率已经超过预期，此时可以提前结束本轮测量", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME31 = ["Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", "Name:cntref_mem_din[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\n说明参见r001d[6]，此域用作写入数据[7:0]配置", '0x00']
PAGE1_NAME30 = ["Name:cntref_mem_din[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\n说明参见r001d[6]，此域用作写入数据[11:8]配置", "Name:cntref_mem_din[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\n说明参见r001d[6]，此域用作写入数据[11:8]配置", "Name:cntref_mem_din[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\n说明参见r001d[6]，此域用作写入数据[11:8]配置", "Name:cntref_mem_din[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\n说明参见r001d[6]，此域用作写入数据[11:8]配置", "Name:cntref_mem_addr[1:0]\nAccess: RW\nReset Value: 2'b0\nDescription:\n说明参见r001d[6]，此位用作写入地址配置", "Name:cntref_mem_addr[1:0]\nAccess: RW\nReset Value: 2'b0\nDescription:\n说明参见r001d[6]，此位用作写入地址配置", "Name:cntref_mem_wre\nAccess: RW\nReset Value: 1'b0\nDescription:\n当r0010[1]=0且r0010[2]=0且r0003[4]=1(为多通道Calibration)时，VCO Tuner的参考计数值从一个4深度寄存器堆中读出，其中将channel号映射成0~3的地址读值。此寄存器堆可以通过寄存器操作进行写入，这个寄存器组就是提供这项功能。\n此位为寄存器堆的写入位，当r001d[5:4]的地址和r001d[3:0]&r001e[7:0]的数据准备好后，往此位先写1再写0，即可写入。", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME29 = ["Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[7:0]\nAccess: RW\nReset Value: 8'hCC\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", '0x00']
PAGE1_NAME28 = ["Name:fsynvcoatcntrefovrdwd[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:fsynvcoatcntrefovrdwd[11:8]\nAccess: RW\nReset Value: 4'h5\nDescription:\nVCO Tuner参考计数寄存器配置值\n当r0010[1]=1或者r0010[2]=1时，VCO Tuner的参考计数值由此寄存器给定。", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", '0x00']
PAGE1_NAME27 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:pmufsynldoiprbptatcore\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn LDO测试信号", "Name:pmufsynldoendlyovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn LDO delay控制信号", "Name:pmufsynldoenovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nfsyn使能信号", "Name:pmurffeldoprbiptatref\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO测试信号", "Name:pmurffeldoiprbptatcore\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO测试信号", "Name:pmurffeldoibst_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO偏置电流增加控制信号", "Name:pmurffeldoibst_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrffe LDO偏置电流增加控制信号的选择\n1‘b1: pmurffeldoibst_ovrd\n1'b0: LDO delay控制信号的取反（!pmurffeldoendly）", '0x00']
PAGE1_NAME26 = ["Name:pmuanaldoibst_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO偏置电流增加控制信号", "Name:pmuanaldoibst_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO偏置电流增加控制信号的选择\n1‘b1: pmuanaldoibst_ovrd\n1'b0: LDO delay控制信号的取反（!pmuanaldoendly）", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 6'b0\nDescription:\n", '0x00']
PAGE1_NAME25 = ["Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\n", "Name:pmuanaldoprbiptatref\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO测试信号", "Name:pmuanaldoiprbptatcore\nAccess: RW\nReset Value: 1'b0\nDescription:\nana LDO测试信号", "Name:oscenstupovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n软件配置模式\n1’b1: 均采用寄存器ovrd信号配置\n1‘b0: 使用IP端口引脚进行打开，端口名字为OSCEN和DIGCLKEN", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", '0x00']
PAGE1_NAME24 = ["Name:implsgenupcfg\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL内部的Impulse Generator模块使能的情况下，\nup输出控制信号。\n1'b0: up信号输出为0；\n1‘b1: up信号输出单周期脉冲。", "Name:implsgen_rst_n\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL内部的Impulse Generator模块的异步复位信号\n1'b0: 复位有效\n1‘b1: 复位释放", "Name:implsgenofstcfg\nAccess: RW\nReset Value: 4'b0\nDescription:\nPLL内部的Impulse Generator模块，脉冲产生时的CP \noffset电流大小配置，仅在一个脉冲周期内有效，脉冲周期外固定为0。仅在implsgenen=1时有意义。", "Name:implsgenofstcfg\nAccess: RW\nReset Value: 4'b0\nDescription:\nPLL内部的Impulse Generator模块，脉冲产生时的CP \noffset电流大小配置，仅在一个脉冲周期内有效，脉冲周期外固定为0。仅在implsgenen=1时有意义。", "Name:implsgenofstcfg\nAccess: RW\nReset Value: 4'b0\nDescription:\nPLL内部的Impulse Generator模块，脉冲产生时的CP \noffset电流大小配置，仅在一个脉冲周期内有效，脉冲周期外固定为0。仅在implsgenen=1时有意义。", "Name:implsgenofstcfg\nAccess: RW\nReset Value: 4'b0\nDescription:\nPLL内部的Impulse Generator模块，脉冲产生时的CP \noffset电流大小配置，仅在一个脉冲周期内有效，脉冲周期外固定为0。仅在implsgenen=1时有意义。", "Name:implsgenen\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL内部的Impulse Generator模块使能信号。\n1’b0: Impulse Generator模块不工作，此时up/down信号，offset电流控制信号，calibration enable信号都从bypass成从数字Calibrator输入。\n1'b1: Impulse Generator模块有效。", "Name:implsgendncfg\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL内部的Impulse Generator模块使能的情况下，\ndown输出控制信号。\n1'b0: down信号输出为0；\n1‘b1: down信号输出单周期脉冲。", '0x00']
PAGE1_NAME23 = ["Name:fsynvco_chgr3_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 3的VCO Tune Word", "Name:fsynvco_chgr3_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 3的VCO Tune Word", "Name:fsynvco_chgr3_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 3的VCO Tune Word", "Name:fsynvco_chgr3_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 3的VCO Tune Word", "Name:fsynvco_chgr3_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 3的VCO Tune Word", "Name:fsynvco_chgr3_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 3的VCO Tune Word", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME22 = ["Name:fsynvco_chgr2_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 2的VCO Tune Word", "Name:fsynvco_chgr2_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 2的VCO Tune Word", "Name:fsynvco_chgr2_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 2的VCO Tune Word", "Name:fsynvco_chgr2_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 2的VCO Tune Word", "Name:fsynvco_chgr2_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 2的VCO Tune Word", "Name:fsynvco_chgr2_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 2的VCO Tune Word", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME21 = ["Name:fsynvco_chgr1_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 1的VCO Tune Word", "Name:fsynvco_chgr1_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 1的VCO Tune Word", "Name:fsynvco_chgr1_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 1的VCO Tune Word", "Name:fsynvco_chgr1_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 1的VCO Tune Word", "Name:fsynvco_chgr1_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 1的VCO Tune Word", "Name:fsynvco_chgr1_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 1的VCO Tune Word", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME20 = ["Name:fsynvco_chgr0_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 0的VCO Tune Word", "Name:fsynvco_chgr0_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 0的VCO Tune Word", "Name:fsynvco_chgr0_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 0的VCO Tune Word", "Name:fsynvco_chgr0_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 0的VCO Tune Word", "Name:fsynvco_chgr0_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 0的VCO Tune Word", "Name:fsynvco_chgr0_ovrdwd\nAccess: RW\nReset Value: 6'h20\nDescription:\nfsynvcoat_chgrovrd_sel=1时，用此寄存器值替代Channal Group 0的VCO Tune Word", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME19 = ["Name:fsynvcoatftuneovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\nVCO Tune Word Override\n当fsynvcoatcalovrd_sel=1时，使用此寄存器作为VCO Tune Word", "Name:fsynvcoatftuneovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\nVCO Tune Word Override\n当fsynvcoatcalovrd_sel=1时，使用此寄存器作为VCO Tune Word", "Name:fsynvcoatftuneovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\nVCO Tune Word Override\n当fsynvcoatcalovrd_sel=1时，使用此寄存器作为VCO Tune Word", "Name:fsynvcoatftuneovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\nVCO Tune Word Override\n当fsynvcoatcalovrd_sel=1时，使用此寄存器作为VCO Tune Word", "Name:fsynvcoatftuneovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\nVCO Tune Word Override\n当fsynvcoatcalovrd_sel=1时，使用此寄存器作为VCO Tune Word", "Name:fsynvcoatftuneovrd\nAccess: RW\nReset Value: 6'h20\nDescription:\nVCO Tune Word Override\n当fsynvcoatcalovrd_sel=1时，使用此寄存器作为VCO Tune Word", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:fsynvcoatcalovrd_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Tune Word Override Select\n1’b0: 使用VCO Tune Word\n1‘b1: 使用fsynvcoatftuneovrd作为VCO Tune Word", '0x00']
PAGE1_NAME18 = ["Name:fsynvcoat_chgrovrd_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Multi-channel Calibration Mode时的寄存器选择\n1'b0: using VCO Tuner Word from calibration result\n1'b1: using Register Configuration VCO Tuner Word，分成4组，分别放在r0013~r0016寄存器", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 7'b0\nDescription:\n", '0x00']
PAGE1_NAME17 = ["Name:fsynvcoaten_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Tuner Enable寄存器控制，在bit[1]=1时有效", "Name:fsynvcoaten_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Tuner Enable寄存器选择\n1‘b0: 不由寄存器直接控制\n1’b1: 由寄存器bit[0]直接控制", "Name:fsynvcoatmchcalovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Tuner强制寄存器配置控制，当此位为1时：\n1. VCO Tuner 6-bit控制字由r3a[5:0]给出\n2. 送入SDM的小数值，由rb3/rb4/rb5寄存器给出\n3. 频率测量比较基准值，由r1b/r1c寄存器给出\n相当于给定一种测试模式，可以固定VCO Tuner控制字，实际测量频率", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 5'b0\nDescription:\n", '0x00']
PAGE1_NAME16 = ["Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[7:0]\nAccess: RO\nReset Value: 8'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", '0x00']
PAGE1_NAME15 = ["Name:fsynvcoat_ncnt[11:8]\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[11:8]\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[11:8]\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:fsynvcoat_ncnt[11:8]\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数，结合calibration分频比\n反映实测频率", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数", "Name:NA\nAccess: RO\nReset Value: 4'b0\nDescription:\nVCO Tuner过程中的频率计数", '0x00']
PAGE1_NAME14 = ["Name:fsynvcoat_calerr\nAccess: RO\nReset Value: 5'b0\nDescription:\nVCO Tuner过程中记录和目标频差最小的误差值", "Name:fsynvcoat_calerr\nAccess: RO\nReset Value: 5'b0\nDescription:\nVCO Tuner过程中记录和目标频差最小的误差值", "Name:fsynvcoat_calerr\nAccess: RO\nReset Value: 5'b0\nDescription:\nVCO Tuner过程中记录和目标频差最小的误差值", "Name:fsynvcoat_calerr\nAccess: RO\nReset Value: 5'b0\nDescription:\nVCO Tuner过程中记录和目标频差最小的误差值", "Name:fsynvcoat_calerr\nAccess: RO\nReset Value: 5'b0\nDescription:\nVCO Tuner过程中记录和目标频差最小的误差值", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", '0x00']
PAGE1_NAME13 = ["Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[7:0]\nAccess: RW\nReset Value: 8'hFF\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", '0x00']
PAGE1_NAME12 = ["Name:implsgenclkcycl[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:implsgenclkcycl[9:8]\nAccess: RW\nReset Value: 2'b0\nDescription:\nimpulse generator enable有效后经过多长时间\n拉起calibration enable信号", "Name:fsynxorstnovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n晶振复位信号\n1’b1: disable\n1‘b0: enable", "Name:fsynxocoreenovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\n晶振使能信号\n1‘b1: enable\n1'b0: disable", "Name:fsyniqdivrx_rst_n_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\ndigctrl_fsyniqdivrxrstn信号(数字到模拟)的选择\n1’b1: 采用fsyniqdivrx_rst_n_ovrd的值\n1‘b0: 系统软件复位(0x80)，或者通过置端口SYBYEN引脚为1，使IP由sleep模式进入standby模式，从而复位PLL的iq分频", "Name:fsyniqdivrx_rst_n_ovrd\nAccess: RW\nReset Value: 1'b0\nDescription:\nTX模式下IQ 2分频器复位使能\n1‘b0: enable\n1'b1: disable", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME11 = ["Name:fsyniqdivcaldivmode\nAccess: RW\nReset Value: 1'b0\nDescription:\nPLL Calibration模式\n1'b1: enable\n1'b0: disable", "Name:fsyniqdivfbdivmode\nAccess: RW\nReset Value: 1'b0\nDescription:\nIQ 2分频器给反馈多模分配器模式使能\n1'b1: enable\n1'b0: disable", "Name:fsyniqdivrxmode\nAccess: RW\nReset Value: 1'b0\nDescription:\nIQ 2分频器RX模式\n1’b1: RX\n1'b0: TX", "Name:fsyniqdivtxmode\nAccess: RW\nReset Value: 1'b0\nDescription:\nIQ 2分频器TX模式\n1’b1: TX\n1'b0: RX", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 4'h0\nDescription:\n", '0x00']
PAGE1_NAME10 = ["Name:fsynchpiofststr\nAccess: RW\nReset Value: 1'b0\nDescription:\n电荷泵Offset current 使能\n1’b1: enable\n1'b0: disable", "Name:fsyniqdivibiasn\nAccess: RW\nReset Value: 3'h4\nDescription:\nIQ 2分频器偏置电流调节控制N型", "Name:fsyniqdivibiasn\nAccess: RW\nReset Value: 3'h4\nDescription:\nIQ 2分频器偏置电流调节控制N型", "Name:fsyniqdivibiasn\nAccess: RW\nReset Value: 3'h4\nDescription:\nIQ 2分频器偏置电流调节控制N型", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:fsynimplsgen_otrgr\nAccess: RO\nReset Value: 1'b0\nDescription:\no_trigger信号，与fsynimplsgenen信号一致", '0x00']
PAGE1_NAME9 = ["Name:fsynvcoat_grp3ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-3 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp3ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-3 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp3ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-3 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp3ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-3 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp3ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-3 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp3ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-3 tx calibration word \nSaved by multi-channel calibrator", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME8 = ["Name:fsynvcoat_grp2ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-2 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp2ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-2 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp2ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-2 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp2ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-2 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp2ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-2 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp2ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-2 tx calibration word \nSaved by multi-channel calibrator", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME7 = ["Name:fsynvcoat_grp1ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-1 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp1ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-1 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp1ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-1 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp1ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-1 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp1ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-1 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp1ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-1 tx calibration word \nSaved by multi-channel calibrator", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME6 = ["Name:fsynvcoat_grp0ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-0 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp0ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-0 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp0ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-0 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp0ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-0 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp0ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-0 tx calibration word \nSaved by multi-channel calibrator", "Name:fsynvcoat_grp0ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nChannel group-0 tx calibration word \nSaved by multi-channel calibrator", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME5 = ["Name:fsynvcoat_ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nVCO电容阵列控制位\n由VCO tuning 决定", "Name:fsynvcoat_ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nVCO电容阵列控制位\n由VCO tuning 决定", "Name:fsynvcoat_ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nVCO电容阵列控制位\n由VCO tuning 决定", "Name:fsynvcoat_ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nVCO电容阵列控制位\n由VCO tuning 决定", "Name:fsynvcoat_ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nVCO电容阵列控制位\n由VCO tuning 决定", "Name:fsynvcoat_ftune\nAccess: RO\nReset Value: 6'h20\nDescription:\nVCO电容阵列控制位\n由VCO tuning 决定", "Name:fsynvcoat_caldone\nAccess: RO\nReset Value: 1'b0\nDescription:\nSingle-channel based calibration mode done", "Name:fsynvcoat_mchcaldone\nAccess: RO\nReset Value: 1'b0\nDescription:\nMulti-channel (curve) based calibration mode done", '0x00']
PAGE1_NAME4 = ["Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 3'b0\nDescription:\n", "Name:fsynvcoatstartmchcal\nAccess: RW\nReset Value: 1'b0\nDescription:\nStart VCO multi-channel calibrator\nCalibration的开关控制，打开此位，才能打开Multi-channel模块的时钟，然后才通过vco_mc模块使能vco_tuner_enable信号，进而启动VCO Tuner的状态机。\n打开VCO Tuner的状态机，也可以通过r0010[0]/r0010[1]强制实现。", "Name:fsynvcoat_cal_mode\nAccess: RW\nReset Value: 1'b0\nDescription:\nVCO Tuner Calibration Mode:\n1'b0: Multi-channel (curve) based calibration mode\n1'b1: Single-channel based calibration mode", "Name:fsynvcoat_finetune\nAccess: RW\nReset Value: 2'b0\nDescription:\n微调模式，bit[5]=1表示为微调模式，在此前提下，bit[6]=0/1为两种不同的微调模式\n2'b00:正常模块\n2'b01: 微调模式0\n2'b10: Reserved\n2'b11: 微调模式1", "Name:fsynvcoat_finetune\nAccess: RW\nReset Value: 2'b0\nDescription:\n微调模式，bit[5]=1表示为微调模式，在此前提下，bit[6]=0/1为两种不同的微调模式\n2'b00:正常模块\n2'b01: 微调模式0\n2'b10: Reserved\n2'b11: 微调模式1", "Name:fsynbw_single_cal\nAccess: RW\nReset Value: 1'b0\nDescription:\n1’b0: Multi-channel calibration\n1'b1: Single-channel calibration", '0x00']
PAGE1_NAME3 = ["Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n", "Name:rxfltrcalen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRX Filter Calibration Enable\nA control to enable all blocks required for RX filter calibration\n1'b0: Individual enable controls are active \n1'b1: All required blocks are enabled\nThe following enable controls are grouped to this control signal \n-rxfltnren \n-rxfltren \n-fsynxobuffen", "Name:fsynbwcalen\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to enable all blocks required for PLL BW calibration\n1'b0: Individual enable controls are active\n1'b1: All required blocks are enabled\nThe following enable controls are grouped to this control signal \n-pmufsynvcoldoen\n-fsyncaldiven\n-fsynfbdiven\n-fsynchpen\n-fsyniqdiven\n-fsyniqdivmxrouten\n-fsynsden\n-fsynvcoen\n-fsynsden", "Name:vcocalen\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to enable all blocks required for VCO calibration\n1'b0: Individual enable controls are active\n1'b1: All required blocks are enabled\nThe following enable controls are grouped to this control signal \n-pmufsynvcoldoen\n-fsyncaldiven\n-fsyniqdiven\n-fsyniqdivmxrouten\n-fsynlfdacen\n-fsynvcoen", "Name:rxdcofstcalen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRx DC offset cal power group enable", "Name:rxfltrofstcalen\nAccess: RW\nReset Value: 1'b0\nDescription:\nRx filter DC offset calibration power group enable\n1'b0: Individual enable controls are active \n1'b1: All required blocks are enabled\nThe following enable controls are grouped to this control signal \n-rxfltnren", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", "Name:NA\nAccess: RO\nReset Value: 2'b0\nDescription:\n", '0x00']
PAGE1_NAME2 = ["Name:rxclkdrvst\nAccess: RW\nReset Value: 1'b0\nDescription:\n拟删除", "Name:rxdatadrvst\nAccess: RW\nReset Value: 1'b0\nDescription:\n拟删除", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\n拟删除", "Name:spimisodrvst\nAccess: RW\nReset Value: 1'b0\nDescription:\n拟删除", "Name:clkout16drvst\nAccess: RW\nReset Value: 1'b0\nDescription:\n拟删除", "Name:tmuxdrvst\nAccess: RW\nReset Value: 1'b0\nDescription:\n拟删除", "Name:rxon_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\nrxon信号来源：\n1'b0 : 寄存器0x00 bit[5]\n1'b1 : 基带", "Name:txon_sel\nAccess: RW\nReset Value: 1'b0\nDescription:\ntxon信号来源：\n1'b0 : 寄存器0x00 bit[6]\n1'b1 : 基带", '0x00']
PAGE1_NAME1 = ["Name:fsynxobuffen\nAccess: RW\nReset Value: 1'b0\nDescription:\nFSYNXO_REFCLK输出使能控制，XO Buffer control\n1'b0: not enabled\n1'b1: enabled", "Name:NA\nAccess: RO\nReset Value: 1'b0\nDescription:\nReserved", "Name:rxtx_mode\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to highlight if the chip is either in RX or TX mode\n1'b1: The chip operates as primary RX \n1'b0: The chip operates as primary TX", "Name:fsyntopen\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to enable all FSYN blocks at once \n1'b0: Individual enable controls are active \n1'b1: All FSYN blocks are enabled\nThe following enable controls are grouped to this control signal \n- pmufsynvcoldoen \n- fsynfbdiven \n- fsynchpen \n- fsyniqdiven \n- fsynsden \n- fsynvcoen \n- fsynxobuffen", "Name:biastopen\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to enable all BIAS blocks at once \n1'b0: Individual enable controls are active \n1'b1: All BIAS blocks are enabled\nThe following enable controls are grouped to this control signal \n- pmuanaldoen \n- pwrbiasen \n- pwrbiasvbgen", "Name:rxtopen\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to enable all RX blocks at once \n1'b0: Individual enable controls are active \n1'b1: All RX blocks are enabled\nThe following enable controls are grouped to this control signal \n- pmurxadcldoen \n- fsyniqdivmxrouten \n- rxlnaen \n- rxfltren \n- rxlimrssien \n- rxmxrpsven \n- rxmxractven \n- rxvgaen \n- saradcen", "Name:txtopen\nAccess: RW\nReset Value: 1'b0\nDescription:\nA control to enable all TX blocks at once\n1'b0: Individual enable controls are active \n1'b1: All TX blocks are enabled\nThe following enable controls are grouped to this control signal \n- pmurxadcldoen \n- fsyniqdivpaouten \n- txpaen", "Name:scheme\nAccess: RW\nReset Value: 1'b0\nDescription:\nControl to enable the chip operation\n1'b0: Using Chip enable & Data in external pins\n1'b1: Using Rx on and Tx on external pins", '0x00']

