# ****begin****解析测试汇总文件****
test_summary_chg = {
    "芯片及代号":    "chip",
    "RF配置文件":   "rf_config_file_path",
    "RF发射测试":   "RF发射测试",
    "RF接收测试":   "RF接收测试",
    "蓝牙指标测试":   "蓝牙指标测试"
}

test_summary_title = [
    "芯片及代号",
    "RF配置文件",
    "RF发射测试",
    "RF接收测试",
    "蓝牙指标测试"
]
# ****end****解析测试汇总文件end****


device_model = {
    "spectrum_analyzer": "N9010B",
    "signal_generator": "E4438C",
    "bluetooth_tester": "CMW",
    "dc_power": "E36312A",
}

import_file_order = [
    "module_set.device_base_api_class",
    "module_set.serial_base_class",
    "module_set.sa_device_api",
    "module_set.sa_device_func",
    "module_set.vsg_device_api",
    "module_set.vsg_device_func",
    "module_set.dcp_device_api",
    "module_set.dcp_device_func",
    "module_set.cmw_device_api",
    "module_set.cmw_device_func",
    "module_set.device_run_func_class",
    "module_set.draw_class",
    "module_set.handle_report_class",
]


def color_print(msg: str, color: str = 'red'):
    if color == 'red':
        print("\033[91m{}\033[0m".format(msg))
    if color == 'green':
        print("\033[92m{}\033[0m".format(msg))
    if color == 'yellow':
        print("\033[93m{}\033[0m".format(msg))
    if color == 'blue':
        print("\033[94m{}\033[0m".format(msg))

# print("\033[91m这段文字是红色的\033[0m")
# print("\033[92m这段文字是绿色的\033[0m")
# print("\033[93m这段文字是黄色的\033[0m")
# print("\033[94m这段文字是蓝色的\033[0m")


test_data = {
    'dc_power': '192.168.1.1',
    'spectrum_analyzer': '***********',
    'signal_generator': '***************',
    'bluetooth_tester': '***********',
    'uart_port': '/dev/ttyUSB0',
    'line_loss': '23',
    'save_report_path': '/home/<USER>/python_projects/gxbtt/report',
    'test_case': {
        'RF接收测试': [
            {
                'name': '接收增益步进测试',
                'file': '/home/<USER>/python_projects/gxbtt/testcases/通用测试用例/RF接收测试/tc_00_case_vsg_rw_data.py'
            }
        ]
    },
    'chip': 'APUS_NRE'
}
