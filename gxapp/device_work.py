import sys
import importlib
import os

from gxpy.gxengine.eventengine import Event, EventEngine, BaseData
from gxapp.bttengine_run_data import BttRunData
from gxapp.public_data import import_file_order, color_print


class DeviceWork:

    def __init__(self, data: BttRunData, cur_tc: str, event_engine: EventEngine):
        self.data = data
        self.cur_tc = cur_tc
        self.event_engine = event_engine

    def check_and_reload_modules(self):
        watchdog_file_directory = './module_set'
        ignore_file = ["__init__.py"]

        # 获取目录下所有文件和子目录指定文件
        modules_to_watch = list()
        for root, dirs, files in os.walk(watchdog_file_directory):
            for file in files:
                if file.endswith('.py') and file not in ignore_file:
                    module_name = file[:-3]
                    modules_to_watch.append(module_name)
        print(modules_to_watch)

        full_modules_to_watch = list()
        for module_name in modules_to_watch:
            full_module_name = "module_set" + '.' + module_name
            full_modules_to_watch.append(full_module_name)
        print(full_modules_to_watch)

        if len(modules_to_watch) != len(import_file_order):
            error_info = "module_set文件夹下的文件数量与import_file_order元素个数不相同，请检查或更新"
            color_print(error_info)
            # return False, error_info

        for module_name in import_file_order:
            if module_name in full_modules_to_watch:
                if sys.modules.get(module_name):
                    print("{} in sys.modules: {}".format(module_name, True))
                    importlib.reload(sys.modules[module_name])
                else:
                    print("{} in sys.modules: {}".format(module_name, False))
            else:
                error_info = "reload的模块：{}， 不在module_set文件夹中"
                return False, error_info
        return True, None

    def importlib_and_reload_cur_testcase(self):
        case_dir = os.path.dirname(self.cur_tc)
        case_name = os.path.basename(self.cur_tc)
        sys.path.append(case_dir)
        module_name = case_name[:-3]

        external_case = importlib.import_module(module_name)

        print("{}是否被执行过{}".format(module_name, module_name in sys.modules))
        if module_name in sys.modules:
            importlib.reload(external_case)
            print("reload module: {}".format(module_name))

        external_case_instance = external_case.CaseRun(data=self.data, event_engine=self.event_engine)

        ret, error = external_case_instance.run()
        if ret is False:
            return False, error
        return True, None

    def do_case(self):
        ret, data = self.check_and_reload_modules()
        if ret is False:
            return False, data
        ret, data = self.importlib_and_reload_cur_testcase()
        if ret is False:
            return False, data
        return True, None


if __name__ == "__main__":
    from gxapp.public_data import test_data
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(current_dir)
    print(parent_path)
    folder_path = os.path.join(parent_path, 'cases')
    print(folder_path)
    event_engine = EventEngine()
    cur_tc = '/home/<USER>/python_projects/gxbtt/testcases/通用测试用例/RF发射测试/tx_tc_00_case_exg.py'
    dw = DeviceWork(data=test_data, cur_tc=cur_tc, event_engine=event_engine)
    dw.do_case()
