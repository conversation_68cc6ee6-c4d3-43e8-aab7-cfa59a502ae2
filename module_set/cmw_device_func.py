import time
import os
import io

from PIL import Image

from module_set.cmw_device_api import CmwApi


class CmwFunc(CmwApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    @staticmethod
    def cmw_func_handle_block_data_format(data: bytes) -> bytes:
        """
        块数据是一种适用于传输大量数据的传输格式。
        * 二进制数据块的结构如下： #<number><length entry><block data>
            * #:
                * '#'在二进制数据块中总是排在第一位
            * <number>:
                * 表示后面的长度条目有多少位数字
            * <length entry>:
                * 表示随后的字节数
            * <block data>:
                * 指定长度的块数据
        * Example:
            * data: #542538xxxxxxxxx......
            * data的数据规律如下：
                * #： 第一位，hash symbol，块数据总是以‘#’开头
                * 5： 第二位，表示随后的5个数字（示例中指的是42538，其长度为5）
                * 42538：表示之后的数据（xxxxxxxxx......）的长度
                * xxxxxxxxx......: 图片数据
        """
        # 取第二位（eg: 542538），5：表示随后的5个数字（这里例子中指的是42538，其长度为5)
        second_numb = int(chr(data[1]))
        # 42538：表示之后的数据（xxxxxxxxxxxxx……）的长度
        db_length = ''
        for i in range(second_numb):
            db_length += chr(data[i + 2])
        db_length = int(db_length)
        # xxxxx……：图片数据
        image_data = data[(2 + second_numb): (2 + second_numb) + db_length]
        return image_data

    def cmw_func_save_screenshot_image(self, save_report_path: str or None, save_image_path: str, image_name: str):
        """
        对设备进行截图并传输到上位机保存
            * save_report_path: 报告保存路径
            * save_image_path: 图片保存路径
            * image_name: 图片保存名词
        """
        if save_report_path is None:
            if not os.path.exists(save_image_path):
                os.mkdir(save_image_path)
            image_file_path = os.path.join(save_image_path, image_name)
        else:
            image_save_path = os.path.join(save_report_path, save_image_path)
            if not os.path.exists(image_save_path):
                os.mkdir(image_save_path)
            image_file_path = os.path.join(image_save_path, image_name)
        block_data = self.cmw_device_hardcopy_query_captures_screenshot_block_data_format()
        image_data = self.cmw_func_handle_block_data_format(data=block_data)
        stream = io.BytesIO(image_data)
        image = Image.open(stream)
        image.save(image_file_path)
