from module_set.vsg_device_api import VsgApi


class VsgFunc(VsgApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def vsg_device_func_print_something(self):
        # print("print in vsg device func 1")
        print("print in vsg device func 2")

    def vsg_device_func_print_other_something(self):
        # print("print other_something 1")
        print("print other_something 2")
