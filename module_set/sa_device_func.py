import os
import io

from PIL import Image
from module_set.sa_device_api import SaApi


class SaFunc(SaApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def sa_func_save_screenshot_image(self, image_dir_path: str, image_name: str):
        """
        对设备进行截图并传输到上位机保存
            * tc_report_dir_path: 图片保存路径
            * image_name: 图片保存名称
        """
        if not os.path.exists(image_dir_path):
            os.makedirs(image_dir_path)
        image_file_save_path = os.path.join(image_dir_path, image_name)
        block_data = self.sa_device_hardcopy_query_captures_screenshot_block_data_format()
        image_data = self.device_handle_block_data_format(data=block_data)
        stream = io.BytesIO(image_data)
        image = Image.open(stream)
        image.save(image_file_save_path)
