import time

from module_set.device_base_api_class import DeviceBaseApi


class SaApi(DeviceBaseApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def sa_device_set_restore_defaults(self, group: str = "ALL"):
        """
        * Full cmd format: ":SYSTem:DEFault <group>"
        * Short cmd format: ":SYST:DEF <group>"
        * group:
            * ALL | ALIGn | INPut | MISC | MODes | PON | UINTerface | SCReen
        * 功能： 提供系统设置组的初始化，包括将整个仪器设置回出厂默认状态的选项.
        """
        cmd = ":SYSTem:DEFault {}".format(group)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_set_mode_preset(self):
        """
        * Mode Preset does the following for the currently active mode:
            – Aborts the currently running measurement.
            – Switches to the default measurement and brings up the default menu for that measurement.
            – Sets most parameters for the Mode and all of its Measurements to a preset state.
            – Clears the input and output buffers.
            – Sets Status Byte to 0.
        * Mode Preset does not cause a Mode switch or affect any Input/Output or System settings (those set in the
        System Settings dialog).
        """
        cmd = ":SYSTem:PRESet"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_set_restore_mode_defaults(self):
        """
        * Most settings within a mode are affected by Mode Preset, but there are some Mode settings that are
            unaffected by a Mode Preset (for example, Noise Floor Extensions, Limit Line data, reference marker
            numbers, etc.)
        * Restore Mode Defaults resets all of these additional settings as well as all of the Mode Preset settings,
            except the RF Source.
        """
        cmd = ":INSTrument:DEFault"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_set_input_output_preset(self):
        """
        * Input/Output Preset resets the group of settings and data associated with the
            Input/Output front-panel key to their default values. These settings are not affected
            by a Mode Preset because they are generally associated with connections to the
            instrument, and most users would not want these resetting every time they pressed
            the Mode Preset key.
        * All the variables set under the Input/Output front panel key are reset by
            Input/Output Preset, including Amplitude Corrections and Data (described in the
            Corrections section), with the 'exception' of RF Source settings, which are
            unaffected.
        * By using Input/Output Preset and Restore Mode Defaults, a full preset of the current
            mode will be performed, with the caveat that since Input/Output Preset is a global
            function it will affect ALL modes.
        """
        cmd = ":SYST:DEF INP"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_set_full_mode_preset(self):
        """
        * Same as doing Mode Preset, Restore Mode Defaults and Input/Output Preset.
            Essentially a factory preset of the current Mode.
        """
        cmd = ":SYSTem:PRESet:FULL"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_application_mode_catalog(self):
        """
        * Full cmd format: ":INSTrument:CATalog?"
        * Short cmd format: ":INST:CAT?"
        * Returns a string containing a comma-separated list of names of all the installed and
            licensed measurement modes (applications). These names can only be used with :INSTrument[:SELect].
        """
        cmd = ":INST:CAT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_query_current_mode_all_licensed_measurement_name(self):
        """
        * Full cmd format: ":CONFigure:CATalog?"
        * Short cmd format: ":CONF:CAT?"
        * returns a quoted string of all licensed measurement names in the current mode.
        * For example, "SAN, CHP, OBW, ACP, PST, TXP, SPUR, SEM, TOI, HARM, LIST, PAVT" for the Spectrum Analyzer mode
        """
        cmd = ":CONF:CAT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_query_current_measurement_name(self):
        """
        * Full cmd format: ":CONFigure?"
        * Short cmd format: ":CONF?"
        * Query the current measurement
        """
        cmd = ":CONF?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_query_current_application_model(self):
        """
        * Full cmd format: ":SYSTem:APPLication[:CURRent][:NAME]?"
        * Short cmd format: ":SYST:APPL?"
        * Returns a string that is the Model Number of the currently selected application (mode).
            * N9060ES1E --> IQ Analyzer and Spectrum Analyzer Applications
        """
        cmd = ":SYST:APPL?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_query_current_application_options(self):
        """
        * Full cmd format: ":SYSTem:APPLication[:CURRent]:OPTion?"
        * Short cmd format: ":SYST:APPL:OPT?"
        * Returns a string that is the Options list of the currently selected application (Mode).
        """
        cmd = ":SYST:APPL:OPT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_select_measurement_application_by_mode_name(self, mode_name: str):
        """
        * Full cmd format: ":INSTrument[:SELect] <mode_id>"
        * The Mode Number in the table below is the parameter for use with the :INSTrument:NSELect command.
        * The Mode Parameter is the parameter for use with the :INSTrument[:SELect] command.
        * Your actual choices will depend upon which applications are installed in your instrument.
        * index to Modes:
            Mode                            Mode Number  Mode Parameter<mode_id>
            5G NR                               109         NR5G
            89601 VSA                           101         VSA89601
            Analog Demod                        234         ADEMOD
            Avionics                            232         AVIONIC
            Bluetooth                           228         BTooth
            Channel Quality / Group Delay       161         CQM
            EMI Receiver                        141         EMI
            GSM/EDGE/EDGE Evo                   13          EDGEGSM
            I/Q Analyzer (Basic)                8           BASIC
            LTE FDD & LTE-A FDD                 107         LTEAFDD
            LTE TDD & LTE-A TDD                 108         LTEATDD
            Measuring Receiver                  233         MRECEIVE
            MSR                                 106         MSR
            Noise Figure                        219         NFIGure
            Phase Noise                         14          PNOISE
            Power Amplifier                     81          PA
            Pulse                               151         PULSEX
            Radio Test                          300         RTS
            Real Time Spectrum Analyzer         2           RTSA
            Remote Language Compatibility       266         RLC
            SCPI Language Compatibility         270         SCPILC
            Sequence Analyzer                   123         SEQAN
            Short Range Comms                   218         SRCOMMS
            Spectrum Analyzer                   1           SA
            Vector Modulation Analyzer          200         VMA
            WCDMA with HSPA+                    9           WCDMA
            WLAN                                217         WLAN
        """
        cmd = ":INSTrument:SELect {}".format(mode_name)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_select_measurement_application_by_mode_number(self, mode_number: int):
        """
        * Full cmd format: ":INSTrument:NSELect <integer>"
        * The Mode Number in the table below is the parameter for use with the :INSTrument:NSELect command.
        * The Mode Parameter is the parameter for use with the :INSTrument[:SELect] command.
        * Your actual choices will depend upon which applications are installed in your instrument.
        * index to Modes:
            Mode                            Mode Number  Mode Parameter<mode_id>
            5G NR                               109         NR5G
            89601 VSA                           101         VSA89601
            Analog Demod                        234         ADEMOD
            Avionics                            232         AVIONIC
            Bluetooth                           228         BTooth
            Channel Quality / Group Delay       161         CQM
            EMI Receiver                        141         EMI
            GSM/EDGE/EDGE Evo                   13          EDGEGSM
            I/Q Analyzer (Basic)                8           BASIC
            LTE FDD & LTE-A FDD                 107         LTEAFDD
            LTE TDD & LTE-A TDD                 108         LTEATDD
            Measuring Receiver                  233         MRECEIVE
            MSR                                 106         MSR
            Noise Figure                        219         NFIGure
            Phase Noise                         14          PNOISE
            Power Amplifier                     81          PA
            Pulse                               151         PULSEX
            Radio Test                          300         RTS
            Real Time Spectrum Analyzer         2           RTSA
            Remote Language Compatibility       266         RLC
            SCPI Language Compatibility         270         SCPILC
            Sequence Analyzer                   123         SEQAN
            Short Range Comms                   218         SRCOMMS
            Spectrum Analyzer                   1           SA
            Vector Modulation Analyzer          200         VMA
            WCDMA with HSPA+                    9           WCDMA
            WLAN                                217         WLAN
        """
        cmd = ":INSTrument:NSELect {}".format(mode_number)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_set_specified_measurement(self, measurement: str, using_default_setting: bool = True):
        """
        * Full cmd format: ":CONFigure:<measurement>[:NDEFault]"
        * Measurement: for the Spectrum Analyzer mode:
            * "SAN, CHP, OBW, ACP, PST, TXP, SPUR, SEM, TOI, HARM, LIST, PAVT"
        * Example:
            * Select and preset the Swept SA measurement:
                * :CONF:SAN
            * Select the Swept SA measurement without presetting:
                * :CONF:SAN:NDEF
        """
        if using_default_setting:
            cmd = ":CONFigure:{}".format(measurement)
        else:
            cmd = ":CONFigure:{}:NDEFault".format(measurement)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_swept_sa_measurement_set_predefined_view(self, view: str):
        """
        * Full cmd format: ":DISPlay:VIEW[:SELect] NORMal | TZOom | SPECtrogram | ZSPan"
        * Example, Set Zone Span view:
            * :DISP:VIEW ZSP
        """
        cmd = ":DISP:VIEW {}".format(view)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_delete_this_screen(self):
        """
        * Full cmd format: ":INSTrument:SCReen:DELete"
        * Short cmd format: ":INST:SCR:DEL"
        * Pressing this button deletes the current Screen (the one with the blue tab). Deleting
            a screen removes it from view and selects the next lower screen in the list of screens.
            If only one screen is configured, it cannot be deleted.
        * Notes:
            * If the screen you are attempting to delete is the only configured screen, the error message “-221,
                Settings conflict; Last screen cannot be deleted” is displayed
            * If the display is disabled (via :DISP:ENAB OFF) then the error message “-221, Settings conflict;
                Screen SCPI cannot be used when Display is disabled” is generated
        """
        cmd = ":INST:SCR:DEL"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_del_all_screen_except_current_screen(self):
        """
        * Full cmd format: ":INSTrument:SCReen:DELete:ALL"
        * Short cmd format: ":INST:SCR:DEL:ALL"
        * 功能： 按下此控件将删除除当前屏幕(带有蓝色选项卡的屏幕)之外的所有屏幕.
        * You can reset the instrument to the power-on configuration by invoking :INST:SCR:DEL:ALL
            followed by :SYSTem:DEFault ALL
        * If the display is disabled (via :DISP:ENAB OFF) then the error message “-221, Settings conflict;
            Screen SCPI cannot be used when Display is disabled” appears
        """
        cmd = ":INST:SCR:DEL:ALL"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_screen_list(self):
        """
        * Full cmd format: ":INSTrument:SCReen:CATalog?"
        * Short cmd format: ":INST:SCR:CAT?"
        * You can obtain a list of currently configured Screens. This permits your remote
            program to manage screens for selection, renaming, or deletion.
        * The query response is a comma separated list of Screen Names. If only 1 Screen is configured, there is
            no trailing comma
        * For R&S compatibility, the following query is also available:
            * :INSTrument:SCReen:LIST?
        """
        cmd = ":INST:SCR:CAT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)         # 返回的是：'"Spectrum Analyzer 6,Spectrum Analyzer 5,Spectrum Analyzer 3"'
        data = eval(data)   # 去除引号："Spectrum Analyzer 6,Spectrum Analyzer 5,Spectrum Analyzer 3"
        screen_list = data.split(',')
        print(screen_list)  # 返回列表：["Spectrum Analyzer 6", "Spectrum Analyzer 5", "Spectrum Analyzer 3"]
        return screen_list

    def sa_device_add_screen(self):
        """
        * Full cmd format: ":INSTrument:SCReen:CREate"
        * Short cmd format: ":INST:SCR:CRE"
        * Every time you add a Screen, the instrument “clones” or “copies” the current Screen
            into the new Screen.
        * Notes:
            * The maximum number of screens is 16. If an attempt to add a screen occurs when the maximum have
                been defined, the error message “-221, Settings conflict; Screen limit reached” appears
            * When you create a new screen the Screen Name is the current Mode name followed by a number
                indicating the instance of the Mode.
        """
        cmd = ":INST:SCR:CRE"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_current_screen_name(self):
        """
        * Full cmd format: ":INSTrument:SCReen:SELect?"
        * Short cmd format: ":INST:SCR:SEL?"
        * Returns the name of the current screen
        """
        cmd = ":INST:SCR:SEL?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_select_screen(self, screen_name: str):
        """
        * Full cmd format: ":INSTrument:SCReen:SELect <screen name>"
        * Short cmd format: ":INST:SCR:SEL <screen name>"
        * Returns the name of the active screen
        * If the <screen name> is specified but not found in the list of Screens, the error message “-224, Illegal
            parameter value; Screen Name not found” is generated
        * If the display is disabled (via :DISP:ENAB OFF) then the error message “-221, Settings conflict;
            Screen SCPI cannot be used when Display is disabled” is generated
        """
        cmd = ":INST:SCR:SEL '{}'".format(screen_name)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_system_error_info(self):
        """
        * Full cmd format: ":SYSTem:ERRor[:NEXT]?"
        * Short cmd format: ":SYST:ERR?"
        * Notes:
            * The return string has the format:
                * <Error Number>,<Error>
                * Where <Error Number> and <Error> are those shown on the Show Errors screen
        """
        cmd = ":SYST:ERR?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_set_center_frequency(self, freq: int or float, unit: str = "Hz"):
        """
        * Full cmd format: "[:SENSe]:FREQuency:CENTer <freq> <unit>"
        * Short cmd format: ":FREQ:CENT <freq> <unit>"
        * Set Center Frequency
        * unit:
            * Hz | KHz | MHz | GHz
        """
        cmd = ":FREQ:CENT {} {}".format(freq, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_current_center_frequency_value(self):
        """
        * Full cmd format: "[:SENSe]:FREQuency:CENTer?"
        * Short cmd format: ":FREQ:CENT?"
        * Return the current value of Center Frequency
        """
        cmd = ":FREQ:CENT?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_adjust_center_frequency_by_cf_step(self, parameter: str):
        """
        * Full cmd format: "[:SENSe]:FREQuency <parameter>"
        * Short cmd format: ":FREQ:CENT <parameter>"
        * Increment/Decrease Center Frequency by the value of CF Step
        * keywords params:
            * UP | DOWN
        """
        cmd = ":FREQ:CENT {}".format(parameter)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

# # start # Swept SA Measurement

    def sa_device_swept_sa_meas_set_span_value(self, freq: int or float, unit: str = "Hz"):
        """
        * Full cmd format: "[:SENSe]:FREQuency:SPAN <freq><unit>"
        * Short cmd format: ":FREQ:SPAN <freq><unit>"
        * Swept SA Measurement Set the span value.
        * unit:
            * Hz | KHz | MHz | GHz
        * Notes:
            * Set Span to zero, switch to Zero Span:
                * :FREQ:SPAN 0 Hz
            * while in Zero Span, switches to Swept span:
                * Sending :FREQ:SPAN 1 MHz
        """
        cmd = ":FREQ:SPAN {} {}".format(freq, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_set_res_bw_filter_type(self, filter_type: str):
        """
        * Full cmd format: "[:SENSe]:BANDwidth|BWIDth:SHAPe <filter_type>"
        * Short cmd format: ":BAND:SHAP <filter_type>"
        * <filter_type>:
            * GAUSsian or GAUS
                * For Gaussian filters, the annotation at the bottom of the screen shows the filter
                    bandwidth type (unless it is Normal) parenthetically between the words “Res BW”
                    and the value, for example:
                    * Res BW 10.0 Hz (Normal bandwidth)
                    * Res BW (Impulse) 14.8 Hz (Impulse bandwidth)
            * FLATtop or FLAT
                * The annotation at the bottom of the screen shows that the Flattop shape is being
                    used, for example:
                    * Res BW (Flattop) 10 Hz
        * Selects the type for the resolution bandwidth filters. In the X-Series, the RBW Filter
            BW menu lets you choose between a Gaussian and Flat Top filter shape, for varying
            measurement conditions.
        """
        cmd = ":BAND:SHAP {}".format(filter_type)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_res_bw_filter_type(self):
        """
        * Full cmd format: "[:SENSe]:BANDwidth|BWIDth:SHAPe?"
        * Short cmd format: ":BAND:SHAP?"
        * Queries the type of the resolution bandwidth filters.
        """
        cmd = ":BAND:SHAP?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_swept_sa_meas_set_res_bw_auto_function(self, function: str or int):
        """
        * Full cmd format: "[:SENSe]:BANDwidth|BWIDth[:RESolution]:AUTO <function>"
        * Short cmd format: ":BWID:AUTO <function>"
        * function:
            * ON | 1
            * OFF | 0
        """
        cmd = ":BWID:AUTO {}".format(function)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_swept_sa_meas_query_res_bw_auto_function_status(self):
        """
        * Full cmd format: "[:SENSe]:BANDwidth|BWIDth[:RESolution]:AUTO?"
        * Short cmd format: ":BWID:AUTO?"
        """
        cmd = ":BWID:AUTO?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_swept_sa_meas_set_res_bw_freq(self, freq: int or float, unit: str):
        """
        * Full cmd format: "[:SENSe]:BANDwidth|BWIDth[:RESolution] <freq> <unit>"
        * Short cmd format: ":BAND <freq> <unit>"
        * Set the res bw
        * unit:
            * Hz | KHz | MHz | GHz
        """
        cmd = ":BAND {} {}".format(freq, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_swept_sa_meas_query_res_bw_value(self):
        """
        * Full cmd format: "[:SENSe]:BANDwidth|BWIDth[:RESolution]?"
        * Short cmd format: ":BAND?"
        * Query the res bw
        """
        cmd = ":BAND?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_set_reference_level(self, ref_level: int or float, unit: str):
        """
        * Full cmd format: ":DISPlay:WINDow[1]:TRACe:Y[:SCALe]:RLEVel <ref_level> <unit>"
        * Short cmd format: ":DISP:WIND:TRAC:Y:RLEV <ref_level> <unit>"
        * Set the reference level
        * unit：
            * dBm
        """
        cmd = ":DISP:WIND:TRAC:Y:RLEV {} {}".format(ref_level, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_reference_level(self):
        """
        * Full cmd format: ":DISPlay:WINDow[1]:TRACe:Y[:SCALe]:RLEVel?"
        * Short cmd format: ":DISP:WIND:TRAC:Y:RLEV?"
        * Query the reference level
        """
        cmd = ":DISP:WIND:TRAC:Y:RLEV?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_set_scale_div(self, rel_ampl: int or float, unit: str):
        """
        * Full cmd format: ":DISPlay:WINDow[1]:TRACe:Y[:SCALe]:PDIVision <rel_ampl> <unit>"
        * Short cmd format: ":DISP:WIND:TRAC:Y:PDIV <ref_level> <unit>"
        * Set the Scale/Div
        * unit：
            * DB
        """
        cmd = ":DISP:WIND:TRAC:Y:PDIV {} {}".format(rel_ampl, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_scale_div(self):
        """
        * Full cmd format: ":DISPlay:WINDow[1]:TRACe:Y[:SCALe]:PDIVision?"
        * Short cmd format: ":DISP:WIND:TRAC:Y:PDIV?"
        * Query the Scale/Div
        """
        cmd = ":DISP:WIND:TRAC:Y:PDIV?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def sa_device_set_peak_search_by_marker_number(self, numb: int = 1):
        """
        * Full cmd format: ":CALCulate:MARKer[1]|2|…|12:MAXimum"
        * Short cmd format: ":CALC:MARK<numb>:MAX"
        * numb:
            * preset: 1
            * range: 1~12
        * Perform a peak search using marker <numb>
        """
        cmd = ":CALC:MARK{}:MAX".format(numb)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_query_marker_amplitude_y_axis_value_by_marker_number(self, numb: int = 1):
        """
        * Full cmd format: ":CALCulate:MARKer[1]|2|…|12:Y?"
        * Short cmd format: ":CALC:MARK<numb>:Y?"
        * numb:
            * preset: 1
            * range: 1~12
        * Query the marker amplitude (Y-axis) value for marker <numb>
        """
        cmd = ":CALC:MARK{}:Y?".format(numb)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_query_marker_frequency_or_time_x_axis_value_by_marker_number(self, numb: int = 1):
        """
        * Full cmd format: ":CALCulate:MARKer[1]|2|…|12:X?"
        * Short cmd format: ":CALC:MARK<numb>:X?"
        * numb:
            * preset: 1
            * range: 1~12
        * Query the marker frequency or time (X-axis) value for marker <numb>
        """
        cmd = ":CALC:MARK{}:X?".format(numb)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_swept_sa_measurement_set_trace_type(self, trace_type: str, trace_numb: int = 1):
        """
        * Full cmd format: ":TRACe[1]|2|…|6:TYPE <trace_type>"
        * Short cmd format: ":TRAC<trace_numb>:TYPE <trace_type>"
        * trace_type:
            * WRITe | AVERage | MAXHold | MINHold
        * trace_numb:
            * preset: 1
            * range: 1~6
        * For Swept SA Measurement (in SA Mode):
            * :TRACe[1]|2|…|6:TYPE WRITe | AVERage | MAXHold | MINHold
        * Preset:
            * Swept SA and Monitor Spectrum: WRITe
            * Following Preset, all traces are cleared (all trace points set to mintracevalue)
        """
        cmd = ":TRAC{}:TYPE {}".format(trace_numb, trace_type)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_all_other_measurement_set_trace_type(self, trace_type: str, meas: str, trace_numb: int = 1):
        """
        * Full cmd format: ":TRACe[1]|2|…|6:TYPE <trace_type>"
        * Short cmd format: ":TRAC<trace_numb>:<meas>:TYPE <trace_type>"
        * trace_type:
            * WRITe | AVERage | MAXHold | MINHold
        * trace_numb:
            * preset: 1
            * range: 1~6
        * meas: for the Spectrum Analyzer mode:
            * "SAN, CHP, OBW, ACP, PST, TXP, SPUR, SEM, TOI, HARM, LIST, PAVT"
            * In this function, the measurement parameters do not contain "SAN"
        * For all other measurements:
            * :TRACe[1]|2|3:<meas>:TYPE WRITe | AVERage | MAXHold | MINHold
            * where <meas> is the identifier for the current measurement
        * Preset:
            * All other measurements: AVERage
            * Following Preset, all traces are cleared (all trace points set to mintracevalue)
        """
        cmd = ":TRAC{}:{}:TYPE {}".format(trace_numb, meas, trace_type)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_swept_sa_meas_set_trace_update_variable_state(self, state: str or int, trace_numb: int = 1):
        """
        * Full cmd format: ":TRACe[1]|2|…|6:UPDate[:STATe] ON | OFF | 1 | 0"
        * Short cmd format: ":TRAC<trace_numb>:UPD <state>"
        * state:
            * str params: ON | OFF
            * int params: 1 | 0
        * trace_numb:
            * preset: 1
            * range: 1~6
        * Preset:
            * For Swept SA Measurement (in SA Mode): 1|0|0|0|0|0
            * ON for Trace 1; OFF for 2–6
        """
        cmd = ":TRAC{}:UPD {}".format(trace_numb, state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_all_other_meas_set_trace_update_variable_state(self, state: str or int, meas: str, trace_numb: int = 1):
        """
        * Full cmd format: ":TRACe[1]|2|3:<meas>:UPDate[:STATe] ON | OFF | 1 | 0"
        * Short cmd format: ":TRAC<trace_numb>:<meas>:UPD <state>"
        * state:
            * str params: ON | OFF
            * int params: 1 | 0
        * trace_numb:
            * preset: 1
            * range: 1~3
        * meas: for the Spectrum Analyzer mode:
            * "SAN, CHP, OBW, ACP, PST, TXP, SPUR, SEM, TOI, HARM, LIST, PAVT"
            * In this function, the measurement parameters do not contain "SAN"
        * Preset:
            * For all other measurements: 1|0|0
            * ON for Trace 1; OFF for 2 & 3
        """
        cmd = ":TRAC{}:{}:UPD {}".format(trace_numb, meas, state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_swept_sa_meas_set_trace_display_variable_state(self, state: str or int, trace_numb: int = 1):
        """
        * Full cmd format: ":TRACe[1]|2|…|6:DISPlay[:STATe] ON | OFF | 1 | 0"
        * Short cmd format: ":TRAC<trace_numb>:DISP <state>"
        * state:
            * str params: ON | OFF
            * int params: 1 | 0
        * trace_numb:
            * preset: 1
            * range: 1~6
        * Make trace N visible.
        * example:
            * Make trace 1 visible: --> 使迹线 1 可见
                * :TRAC2:DISP 1
            * Blank trace 3:  --> 空白迹线 3
                * :TRAC3:DISP 3
        * Preset:
            * For Swept SA Measurement (in SA Mode): 1|0|0|0|0|0
                * ON for Trace 1; OFF for 2–6
        """
        cmd = ":TRAC{}:DISP {}".format(trace_numb, state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_all_other_meas_set_trace_display_variable_state(self, state: str or int, meas: str, trace_numb: int = 1):
        """
        * Full cmd format: ":TRACe[1]|2|3:<meas>:DISPlay[:STATe] ON | OFF | 1 | 0"
        * Short cmd format: ":TRAC<trace_numb>:<meas>:DISP <state>"
        * state:
            * str params: ON | OFF
            * int params: 1 | 0
        * trace_numb:
            * preset: 1
            * range: 1~3
        * meas: for the Spectrum Analyzer mode:
            * "SAN, CHP, OBW, ACP, PST, TXP, SPUR, SEM, TOI, HARM, LIST, PAVT"
            * In this function, the measurement parameters do not contain "SAN"
        * Preset:
            * For all other measurements: 1|0|0
                * ON for Trace 1; OFF for 2 & 3
        """
        cmd = ":TRAC{}:{}:DISP {}".format(trace_numb, meas, state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_swept_sa_meas_set_view_blank(self, option: str, trace_numb: int = 1):
        """
        * Lets you set the state of the two trace variables: Update and Display. The choices
            available in this dropdown menu are:
        * option:
            * Active:
                * Update and Display both ON
            * View:
                * Update OFF; Display ON
            * Blank:
                * Update OFF; Display OFF
            * Background:
                * Update ON, Display OFF
        * trace_numb:
            * preset: 1
            * range: 1~6
        """
        if option == "Active":
            update_state = "ON"
            display_state = "ON"
        elif option == "View":
            update_state = "OFF"
            display_state = "ON"
        elif option == "Blank":
            update_state = "OFF"
            display_state = "OFF"
        elif option == "Background":
            update_state = "ON"
            display_state = "OFF"
        else:
            print("option param error")
            return False
        self.sa_device_swept_sa_meas_set_trace_update_variable_state(state=update_state, trace_numb=trace_numb)
        self.sa_device_swept_sa_meas_set_trace_display_variable_state(state=display_state, trace_numb=trace_numb)
        return True

    def sa_device_all_other_meas_set_view_blank(self, option: str, meas: str, trace_numb: int = 1):
        """
        * Lets you set the state of the two trace variables: Update and Display. The choices
            available in this dropdown menu are:
        * option:
            * Active:
                * Update and Display both ON
            * View:
                * Update OFF; Display ON
            * Blank:
                * Update OFF; Display OFF
            * Background:
                * Update ON, Display OFF
        * meas: for the Spectrum Analyzer mode:
            * "SAN, CHP, OBW, ACP, PST, TXP, SPUR, SEM, TOI, HARM, LIST, PAVT"
            * In this function, the measurement parameters do not contain "SAN"
        * trace_numb:
            * preset: 1
            * range: 1~3
        """
        if option == "Active":
            update_state = "ON"
            display_state = "ON"
        elif option == "View":
            update_state = "OFF"
            display_state = "ON"
        elif option == "Blank":
            update_state = "OFF"
            display_state = "OFF"
        elif option == "Background":
            update_state = "ON"
            display_state = "OFF"
        else:
            print("option param error")
            return False
        self.sa_device_all_other_meas_set_trace_update_variable_state(
            state=update_state, meas=meas, trace_numb=trace_numb)
        self.sa_device_all_other_meas_set_trace_display_variable_state(
            state=display_state, meas=meas, trace_numb=trace_numb)
        return True

    def sa_device_marker_set_marker_table_state(self, state: str or int):
        """
        * Full cmd format: ":CALCulate:MARKer:TABLe[:STATe] OFF | ON | 0 | 1"
        * Short cmd format: ":CALC:MARK:TABL <state>"
        * state:
            * str params: ON | OFF
            * int params: 1 | 0
        """
        cmd = ":CALC:MARK:TABL {}".format(state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

# # end # Swept SA Measurement

# # start # Channel Power Measurement

    def sa_device_ch_power_meas_set_span_value(self, freq: int or float, unit: str = "Hz"):
        """
        * Full cmd format: "[:SENSe]:CHPower:FREQuency:SPAN <freq><unit>"
        * Short cmd format: ":CHP:FREQ:SPAN <freq><unit>"
        * Channel Power Measurement: Set the span value.
        * unit:
            * Hz | KHz | MHz | GHz
        """
        cmd = ":CHP:FREQ:SPAN {} {}".format(freq, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_ch_power_meas_query_span_value(self, ):
        """
        * Full cmd format: "[:SENSe]:CHPower:FREQuency:SPAN?"
        * Short cmd format: ":CHP:FREQ:SPAN?"
        * Channel Power Measurement: Query the span value.
        """
        cmd = ":CHP:FREQ:SPAN?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_set_res_bw_freq(self, freq: int or float, unit: str):
        """
        * Full cmd format: "[:SENSe]:CHPower:BANDwidth[:RESolution] <freq> <unit>"
        * Short cmd format: ":CHP:BAND <freq> <unit>"
        * Channel Power Measurement: Set the res bw.
        * unit:
            * Hz | KHz | MHz | GHz
        """
        cmd = ":CHP:BAND {} {}".format(freq, unit)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_ch_power_meas_query_res_bw_value(self):
        """
        * Full cmd format: "[:SENSe]:CHPower:BANDwidth[:RESolution]?"
        * Short cmd format: ":CHP:BAND?"
        * Channel Power Measurement: Query the res bw.
        """
        cmd = ":CHP:BAND?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_set_res_bw_auto_function(self, function: str or int):
        """
        * Full cmd format: "[:SENSe]:CHPower:BANDwidth[:RESolution]:AUTO <function>"
        * Short cmd format: ":CHP:BWID:AUTO <function>"
        * function:
            * ON | 1
            * OFF | 0
        """
        cmd = ":CHP:BWID:AUTO {}".format(function)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_ch_power_meas_query_res_bw_auto_function_status(self):
        """
        * Full cmd format: "[:SENSe]:CHPower:BANDwidth[:RESolution]:AUTO?"
        * Short cmd format: ":CHP:BWID:AUTO?"
        """
        cmd = ":CHP:BWID:AUTO?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_set_peak_search_by_marker_number(self, numb: int = 1):
        """
        * Full cmd format: ":CALCulate:CHPower:MARKer[1]|2|…|12:MAXimum"
        * Short cmd format: ":CALC:CHP:MARK<numb>:MAX"
        * numb:
            * preset: 1
            * range: 1~12
        * Perform a peak search using marker <numb>
        """
        cmd = ":CALC:CHP:MARK{}:MAX".format(numb)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_ch_power_meas_query_marker_amplitude_y_axis_value_by_marker_number(self, numb: int = 1):
        """
        * Full cmd format: ":CALCulate:CHPower:MARKer[1]|2|…|12:Y?"
        * Short cmd format: ":CALC:CHP:MARK<numb>:Y?"
        * numb:
            * preset: 1
            * range: 1~12
        * Query the marker amplitude (Y-axis) value for marker <numb>
        """
        cmd = ":CALC:CHP:MARK{}:Y?".format(numb)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_query_marker_frequency_or_time_x_axis_value_by_marker_number(self, numb: int = 1):
        """
        * Full cmd format: ":CALCulate:CHPower:MARKer[1]|2|…|12:X?"
        * Short cmd format: ":CALC:CHP:MARK<numb>:X?"
        * numb:
            * preset: 1
            * range: 1~12
        * Query the marker frequency or time (X-axis) value for marker <numb>
        """
        cmd = ":CALC:CHP:MARK{}:X?".format(numb)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_query_retrieves_data_defined_by_n(self, n: int = 1):
        """
        * Full cmd format: ":FETCh:CHPower[n]?"
        * Short cmd format: ":FETC:CHP[n]?"
        * 检索由n定义的数据
            * n == 1 or not specified
                * return --> 返回标量结果:(Channel Power, PSD (Power Spectral Density))
                    * Channel Power: 一个浮点数，表示指定积分带宽内的总通道功率
                    * PSD (Power Spectral Density): 指定单位带宽内的功率。单位带宽由第628页的“PSD单位”选择；dBm/Hz或dBm/MHz
            * n == 2:
                * return --> 返回浮点数，即跟踪1的信号功率（dBm/分辨率带宽）的捕获跟踪数据。捕获的跟踪数据的频率跨度由第613页的“跨度”指定
            * n == 3:
                * return --> n/a
            * n == 4:
                * return --> n/a
            * n == 5:
                * return --> 返回浮点数，即跟踪2的信号功率（dBm/分辨率带宽）的捕获跟踪数据。捕获的跟踪数据的频率跨度由span指定
            * n == 6:
                * return --> 返回浮点数，即跟踪3的信号功率（dBm/分辨率带宽）的捕获跟踪数据。捕获的跟踪数据的频率跨度由span指定
        """
        cmd = ":FETCh:CHPower{}?".format(n)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_query_the_channel_power_dBm(self):
        """
        * Full cmd format: ":FETCh:CHPower:CHPower?"
        * Short cmd format: ":FETC:CHP:CHP?"
        * 检索由n定义的通道功率（dBm）
        """
        cmd = ":FETCh:CHPower:CHPower?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

    def sa_device_ch_power_meas_query_the_power_spectral_density_dBmHz(self):
        """
        * Full cmd format: ":FETCh:CHPower:DENSity?"
        * Short cmd format: ":FETC:CHP:DENS?"
        * 检索由n定义的功率谱密度（dBm/Hz）
        """
        cmd = ":FETCh:CHPower:DENSity?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)
        return data

# # end # Channel Power Measurement

    def sa_device_store_current_screen_image(self, filename: str):
        """
        * Full cmd format: ":MMEMory:STORe:SCReen <filename>"
        * Short cmd format: ":MMEM:STOR:SCR <filename>"
        * <filename>:
            * example: "myScreen.png"
        * Selects a file for saving the contents of the display.
        * Note:
            * The default path for State Files is: "My Documents\<mode name>\screen"
                where <mode name> is the parameter used to select the mode with :INST:SEL, for
                example, SA for Spectrum Analyzer Mode.
        * example:
            * Store the current screen image in the file MyScreenFile.png in the default directory:
                :MMEM:STOR:SCR "myScreen.png"
        """
        cmd = ':MMEM:STOR:SCR "{}"'.format(filename)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def sa_device_hardcopy_query_captures_screenshot_block_data_format(self):
        """
        截图并以块数据格式返回结果
            * full cmd format: ":HCOPy:SDUMp:DATA?"
            * short cmd format: ":HCOP:SDUM:DATA?"
            * Return values:
                * dblock Data: Screenshot in 488.2 block data format
        """
        cmd = ":HCOPy:SDUMp:DATA?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.1)
        data = self.recv_image_data()
        return data

    def sa_device_set_peak_search_mode(self, mode: str):
        """
        * Full cmd format: ":CALCulate:MARKer:PEAK:SEARch:MODE <mode>"
        * Short cmd format: ":CALC:MARK:PEAK:SEAR:MODE <mode>"
        * mode:
            * MAXimum  --> Set Highest Peak mode --> 设置最高峰模式
            * PARameter --> Set Excursion & Threshold mode --> 设置偏移和阈值模式
        * Lets you specify what kind of search you want to do when Peak Search is pressed
            (or the equivalent command sent).
        """
        cmd = ":CALC:MARK:PEAK:SEAR:MODE {}".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()