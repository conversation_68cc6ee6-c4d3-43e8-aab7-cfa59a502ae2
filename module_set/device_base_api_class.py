import socket
import time
from datetime import datetime


class DeviceBaseApi:

    def __init__(self, ip: str, port: int):
        self.ip = ip
        self.port = port
        self.recv_buffer_size = 128 * 1024      # 设置接收缓冲区大小为128kb
        self.client = socket.socket(family=socket.AF_INET, type=socket.SOCK_STREAM)
        self.client.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, self.recv_buffer_size)
        self.client.settimeout(5)
        self.client.connect((self.ip, self.port))

    def close_client(self):
        self.client.close()

    def send_data(self, data: str):
        data = data.encode("utf-8")
        print("send data: {}".format(data))
        try:
            self.client.send(data)
        except Exception as e:
            return False
        return True

    def recv_data(self):
        data = self.client.recv(4096)
        print("recv data: {}".format(data))
        try:
            data = data.decode("utf-8").strip()
        except Exception as e:
            return False, None
        return True, data

    def recv_nbyte_data(self):
        # 定义每个块的大小
        block_size = 4096

        # 打开文件以写入接收到的数据
        file = open('received_file.txt', 'wb')

        # 循环接收数据块
        while True:
            # 调用socket.recv()方法接收数据块
            try:
                data = self.client.recv(block_size)

                # 如果接收到的数据为空，则表示连接已关闭或出现错误
                if not data:
                    break

                # 将接收到的数据块写入文件
                file.write(data)

            except socket.timeout:
                break
        # 关闭文件
        file.close()
        # data = self.client.recv(1024000)
        # print("recv data: {}".format(data))
        # return True, data

    def recv_image_data(self):
        stream_data = b''
        # 定义每个块的大小
        block_size = 4096 * 4
        while True:
            try:
                data = self.client.recv(block_size)
                stream_data += data
                ret = self.check_transmission_data_end(data=stream_data)
                if ret:
                    return stream_data
            except socket.timeout:
                break
        return stream_data

    @staticmethod
    def cmd_line_ending_to_add_carriage_return_and_newline(cmd: str):
        new_cmd = cmd + "\r\n"
        return new_cmd

    def device_check_operation_complete(self):
        """确认设备操作是否完成"""
        while True:
            cmd = "*OPC?"
            new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
            self.send_data(data=new_cmd)
            time.sleep(0.01)
            ret, data = self.recv_data()
            if data[0] == "1":       # *RST: b'1\n'
                break

    def device_host_id_query(self):
        """
        Returns the Host ID as a string, such as:
            * N9010B,MY63440212
        """
        cmd = ":SYSTem:HID?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print("host id: {}".format(data))

    def device_identification_query(self):
        """
        The response is organized into four fields separated by commas. The field definitions are as follows:
            1. Manufacturer
            2. Model
            3. Serial number
            4. Firmware version
        Returns instrument identification information, such as:
            * Keysight Technologies,N9010B,MY63440212,A.33.03
        """
        cmd = "*IDN?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        return data

    def device_set_reset(self):
        """
        * *RST is equivalent to :SYST:PRES;:INIT:CONT OFF, which is a Mode Preset in the
            Single measurement state. This command is preferred over the Mode Preset
            command :SYST:PRES, because optimal remote programming occurs with the
            instrument in the Single measurement state.
        * *RST clears all pending OPC bits and sets the Status Byte to 0.
        """
        cmd = "*RST"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def device_handle_block_data_format(self, data: bytes) -> bytes:
        """
        块数据是一种适用于传输大量数据的传输格式。
        * 二进制数据块的结构如下： #<number><length entry><block data>
            * #:
                * '#'在二进制数据块中总是排在第一位
            * <number>:
                * 表示后面的长度条目有多少位数字
            * <length entry>:
                * 表示随后的字节数
            * <block data>:
                * 指定长度的块数据
        * Example:
            * data: #542538xxxxxxxxx......
            * data的数据规律如下：
                * #： 第一位，hash symbol，块数据总是以‘#’开头
                * 5： 第二位，表示随后的5个数字（示例中指的是42538，其长度为5）
                * 42538：表示之后的数据（xxxxxxxxx......）的长度
                * xxxxxxxxx......: 图片数据
        """
        # 取第二位（eg: 542538），5：表示随后的5个数字（这里例子中指的是42538，其长度为5)
        second_numb = int(chr(data[1]))
        # 42538：表示之后的数据（xxxxxxxxxxxxx……）的长度
        db_length = ''
        for i in range(second_numb):
            db_length += chr(data[i + 2])
        db_length = int(db_length)
        # xxxxx……：图片数据
        image_data = data[(2 + second_numb): (2 + second_numb) + db_length]
        return image_data

    def check_transmission_data_end(self, data: bytes):
        # 取第二位（eg: 542538），5：表示随后的5个数字（这里例子中指的是42538，其长度为5)
        second_numb = int(chr(data[1]))
        # 42538：表示之后的数据（xxxxxxxxxxxxx……）的长度
        db_length = ''
        for i in range(second_numb):
            db_length += chr(data[i + 2])
        db_length = int(db_length)
        if len(data[(2 + second_numb): (2 + second_numb) + db_length]) == db_length:
            return True
        return False
