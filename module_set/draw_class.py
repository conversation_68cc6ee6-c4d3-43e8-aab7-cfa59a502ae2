import matplotlib
import matplotlib.pyplot as plt
from io import BytesIO
matplotlib.use('agg')


class MatplotlibPlotter:

    def __init__(self, x_axis_data: list, y_axis_data: list):
        self.x_axis_data = x_axis_data
        self.y_axis_data = y_axis_data

    def plot_line_chart(self, title: str, x_label: str, y_label: str, legends: list or None = None):
        ret = self.is_nested_list(lst=self.y_axis_data)
        print(ret)
        if ret is False:
            plot_buffer = self.draw_single_line_graph(
                title=title, x_label=x_label, y_label=y_label, legends=legends)
        else:
            plot_buffer = self.draw_multi_line_graph(
                title=title, x_label=x_label, y_label=y_label, legends=legends)
        return plot_buffer

    @staticmethod
    def is_nested_list(lst):
        return all(isinstance(x, list) for x in lst)

    def draw_single_line_graph(self, title: str, x_label: str, y_label: str, legends: list or None = None):
        if legends is None:
            legends = ["Line 1"]
        # 每次操作前清空图表
        plt.clf()

        plt.plot(self.x_axis_data, self.y_axis_data, 'o-', label=legends[0])
        plt.rcParams['font.sans-serif'] = ['SimHei']    # 设置默认字体为黑体
        plt.rcParams['axes.unicode_minus'] = False      # 用来正常显示负号
        plt.title(title)
        plt.xlabel(x_label, fontproperties='SimHei')
        plt.ylabel(y_label, fontproperties='SimHei')
        plt.legend()
        plot_buffer = BytesIO()
        plt.savefig(plot_buffer, format='png')
        plot_buffer.seek(0)
        return plot_buffer

    def draw_multi_line_graph(self, title: str, x_label: str, y_label: str, legends: list or None = None):
        if legends is None:
            legends = ["Line {}".format(i + 1) for i in range(len(self.y_axis_data))]
        # 每次操作前清空图表
        plt.clf()
        for i in range(len(self.y_axis_data)):
            plt.plot(self.x_axis_data, self.y_axis_data[i], 'o-', label=legends[i])

        plt.rcParams['font.sans-serif'] = ['SimHei']    # 设置默认字体为黑体
        plt.rcParams['axes.unicode_minus'] = False      # 用来正常显示负号
        plt.title(title)
        plt.xlabel(x_label, fontproperties='SimHei')
        plt.ylabel(y_label, fontproperties='SimHei')
        plt.legend()
        plot_buffer = BytesIO()
        plt.savefig(plot_buffer, format='png')
        plot_buffer.seek(0)
        return plot_buffer

    def plot_bar_chart(self, title: str, x_label: str, y_label: str):
        # 每次操作前清空图表
        plt.clf()
        plt.bar(self.x_axis_data, self.y_axis_data)
        # for i, value in enumerate(self.y_axis_data):
        #     plt.text(i, value, str(value), ha='center', va='bottom')
        plt.rcParams['font.sans-serif'] = ['SimHei']    # 设置默认字体为黑体
        plt.rcParams['axes.unicode_minus'] = False      # 用来正常显示负号
        plt.title(title)
        plt.xlabel(x_label, fontproperties='SimHei')
        plt.ylabel(y_label, fontproperties='SimHei')
        plot_buffer = BytesIO()
        plt.savefig(plot_buffer, format='png')
        plot_buffer.seek(0)
        return plot_buffer
