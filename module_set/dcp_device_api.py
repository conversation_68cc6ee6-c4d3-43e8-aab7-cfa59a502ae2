import time

from module_set.device_base_api_class import DeviceBaseApi


class DcPowerApi(DeviceBaseApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def dcp_device_set_power_supply_operation_mode(self, mode: str):
        """
        * Full cmd format: "OUTPut:PAIR <mode>"
        * Short cmd format: "OUTP:PAIR <mode>"
        * <mode>:
            * OFF
            * PARallel --> 并联
            * SERies --> 串联
        * The command specifies the power supply's operation mode.
            Coupling and tracking must not be used in when the instrument is operating in parallel or series.
        """
        cmd = "OUTP:PAIR {}".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_power_supply_operation_mode(self):
        """
        * Full cmd format: "OUTPut:PAIR?"
        * Short cmd format: "OUTP:PAIR?"
        * The query returns the power supply's operation mode.
        """
        cmd = "OUTP:PAIR?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_display_state(self, state: str or int):
        """
        * Full cmd format: "DISPlay[:WINDow][:STATe] <state>"
        * Short cmd format: "DISP <state>"
        * <state>:
            * ON | 1
            * OFF | 0
        * The command turns the front-panel display off or on.
            When the display is turned off, outputs are not sent to the display and all annunciators are disabled.
            The display state is automatically turned on when you return to the local mode.
            Press and hold [Local] for a few seconds to return to the local mode.
        """
        cmd = "DISP {}".format(state)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_display_state(self):
        """
        * Full cmd format: "DISPlay[:WINDow][:STATe]?"
        * Short cmd format: "DISP?"
        * The query returns 0 (OFF) or 1 (ON).
        """
        cmd = "OUTP:PAIR?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_specify_channel_output_state(self, state: str or int, chanlist: str):
        """
        * Full cmd format: "OUTPut[:STATe] <state>, (@<chanlist>)"
        * Short cmd format: "OUTP <state>, (@<chanlist>)"
        * <state>:
            * ON | 1
            * OFF | 0
        * <chanlist>:
            * either list channels
                * 1 or 2 or 3
                * 1,2 or 2,3
                * 3,1,2
            * list a range of channels
                * 1:3
                * 2:3
        * The command enables or disables the specified output.
            The names CH1, CH2, and CH3 are simply other names for P6V, P25V, and N25V, respectively.
            At *RST, all outputs are off.
        * NOTE:
            * You cannot enable or disable the outputs individually in E3631A mode for the power supply.
                All the three chan-nels will be turned ON or OFF at the same time in E3631A mode.
                However, in general mode, you can enable or disable outputs independently.
                For output query, if no channel is specified, the returned value is the output
                state based on current selected channel INSTrument:SELect?.
            * If output sequencing is enabled, the query returns the configuration state instead of
                the actual output state. For example, if you have a 10 s output delay and query the
                output state right after you turn the output on, the query will return 1 (ON) even
                though the actual output will be off until the delay ends.
            * Enabling or disabling any coupled output causes all coupled outputs to turn on or off
                according to their user-programmed delays and programming levels. If one coupled channel
                trips (overvoltage, overcurrent, or over-temperature), the other coupled channels are not impacted.
        """
        cmd = "OUTP {} (@{})".format(state, chanlist)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_specify_channel_output_state(self, chanlist: str = None):
        """
        * Full cmd format: "OUTPut[:STATe]?, [(@<chanlist>)]"
        * Short cmd format: "OUTP?, (@<chanlist>)"
        * <chanlist>:
            * either list channels
                * 1 or 2 or 3
        * The query returns the output state of the power supply. The returned value is 0 (OFF) or 1 (ON).
        """
        if chanlist is None:
            cmd = "OUTP?"
        else:
            cmd = "OUTP? (@{})".format(chanlist)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_channel_meter_view_mode(self, mode: str):
        """
        * Full cmd format: "DISPlay[:WINDow]:VIEW <mode>"
        * Short cmd format: "DISP:VIEW <mode>"
        * <mode>:
            * METER1
                * METER1 displays one big output channel with 2 small less detailed channels.
            * METER3
                * METER3 displays all 3 output channels.
        * The command selects 1- or 3-channel meter view.
        """
        cmd = "DISP:VIEW {}".format(mode)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_channel_meter_view_mode(self):
        """
        * Full cmd format: "DISPlay[:WINDow]:VIEW?"
        * Short cmd format: "DISP:VIEW?"
        * The query returns METER1 or METER3.
        """
        cmd = "DISP:VIEW?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_immediate_output_voltage_level(self, voltage: int or float or str, chanlist: str):
        """
        * Full cmd format: "[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] <voltage>, (@<chanlist>)"
        * Short cmd format: "VOLT <voltage>, (@<chanlist>)"
        * <voltage>:
            * value:
                * ch1: range: 0V ~ 6.18V
                * ch2: range: 0V ~ 25.75V
                * ch3: range: 0V ~ 25.75V
            * string:
                * MINimum:
                    * ch1, ch2, ch3: 0V
                * MAXimum:
                    * ch1: 6.18V
                    * ch2: 25.75V
                    * ch3: 25.75V
                * DEFault:
                    * ch1, ch2, ch3: 0V
        * <chanlist>:
            * either list channels
                * 1 or 2 or 3
        * The command programs the immediate output voltage level of the output in volts.
            The immediate level is the voltage limit value of the selected output with the INSTrument command.
        * example:
            Sets the output voltage level to 20 V: VOLT 20, (@2)
        """
        cmd = "VOLT {}, (@{})".format(voltage, chanlist)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_immediate_output_voltage_level(self, chanlist: str, kws: str = None):
        """
        * Full cmd format: "[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]? <kws>, (@<chanlist>)"
        * Short cmd format: "VOLT? <kws>, (@<chanlist>)"
        * <kws>:
            * MINimum:
                * return the lowest programmable voltage levels respectively for the selected range.
            * MAXimum:
                * return the highest programmable voltage levels respectively for the selected range.
            * DEFault
                * return the default programmable voltage levels respectively for the selected range.
        * <chanlist>:
            * either list channels
                * 1 or 2 or 3
        * The query returns the output voltage level of the output in volts.
        """
        if kws is None:
            cmd = "VOLT?, (@{})".format(kws, chanlist)
        else:
            cmd = "VOLT? {}, (@{})".format(kws, chanlist)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_immediate_output_current_level(self, current: int or float or str, chanlist: str):
        """
        * Full cmd format: "[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] <current>, (@<chanlist>)"
        * Short cmd format: "CURR <current>, (@<chanlist>)"
        * <current>:
            * value:
                * ch1: range: 0.001A ~ 5.15A
                * ch2: range: 0.001A ~ 1.03A
                * ch3: range: 0.001A ~ 1.03A
            * string:
                * MINimum:
                    * ch1, ch2, ch3: 0.001A
                * MAXimum:
                    * ch1: 5.15A
                    * ch2: 1.03A
                    * ch3: 1.03A
                * DEFault:
                    * E36312A:
                        * ch1: (DEF = 5 A)
                        * ch2: (DEF = 1 A)
                        * ch3: (DEF = 1 A)
                    * E36312A (parallel mode)
                        * ch1: (DEF = 5 A)
                        * ch2: (DEF = 2 A)
        * <chanlist>:
            * either list channels
                * 1 or 2 or 3
        * The command directly programs the immediate current level of the power supply in amperes.
            The immediate level is the current limit value of the output selected with the INSTrument command.
        * example:
            Sets the output current level to 3 A: CURR 3, (@1)
        """
        cmd = "CURR {}, (@{})".format(current, chanlist)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_immediate_output_current_level(self, chanlist: str, kws: str = None):
        """
        * Full cmd format: "[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude]? <kws>, (@<chanlist>)"
        * Short cmd format: "CURR? <kws>, (@<chanlist>)"
        * <kws>:
            * MINimum:
                * return the lowest programmable current levels respectively for the selected range.
            * MAXimum:
                * return the highest programmable current levels respectively for the selected range.
            * DEFault
                * return the default programmable current levels respectively for the selected range.
        * <chanlist>:
            * either list channels
                * 1 or 2 or 3
        * The query returns a number in the form +n.nnnnnnnnE+nn for each channel specified.
        """
        if kws is None:
            cmd = "CURR?, (@{})".format(kws, chanlist)
        else:
            cmd = "CURR? {}, (@{})".format(kws, chanlist)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_select_programmed_output_channel(self, channel: str):
        """
        * Full cmd format: "INSTrument[:SELect] <channel>"
        * Short cmd format: "INST <channel>"
        * <channel>:
            * P6V or CH1
            * P25V or CH2
            * N25V or CH3
        * The command selects the output to be programmed.
        * The names CH1, CH2, and CH3 are simply other names for P6V, P25V, and N25V, respectively.
            When one output is selected, the other outputs are unavailable for programming.
            The subsystems that are affected by the INSTrument command are [SOURce:]CURRent, [SOURce:]VOLTage,
            MEASure, and CALibration. P6V is the identifier for +6 V output, P25V is for +25 V output
            and N25V is for -25 V output.
        * example:
            Selects channel 1 to be programmed: INST P6V
        """
        cmd = "INST {}".format(channel)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_query_select_programmed_output_channel(self):
        """
        * Full cmd format: "INSTrument[:SELect]?"
        * Short cmd format: "INST?"
        * The query returns the output currently selected by INSTrument [:SELect]
            or INSTrument:NSELect as either “P6V”, “P25V”, or “N25V”.
        """
        cmd = "INST?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)

    def dcp_device_set_specify_channel_output_voltage_and_current_level(
            self, channel: str, voltage: int or float or str, current: int or float or str):
        """
        * Full cmd format: "APPLy <channel>, <voltage>, <current>"
        * Short cmd format: "APPL <channel>, <voltage>, <current>"
        * <channel>:
            * P6V or CH1
            * P25V or CH2
            * N25V or CH3
        * <voltage>:
            * value:
                * ch1: range: 0V ~ 6.18V
                * ch2: range: 0V ~ 25.75V
                * ch3: range: 0V ~ 25.75V
            * string:
                * MINimum:
                    * ch1, ch2, ch3: 0V
                * MAXimum:
                    * ch1: 6.18V
                    * ch2: 25.75V
                    * ch3: 25.75V
                * DEFault:
                    * ch1, ch2, ch3: 0V
        * <current>:
            * value:
                * ch1: range: 0.001A ~ 5.15A
                * ch2: range: 0.001A ~ 1.03A
                * ch3: range: 0.001A ~ 1.03A
            * string:
                * MINimum:
                    * ch1, ch2, ch3: 0.001A
                * MAXimum:
                    * ch1: 5.15A
                    * ch2: 1.03A
                    * ch3: 1.03A
                * DEFault:
                    * ch1: (DEF = 5 A)
                    * ch2: (DEF = 1 A)
                    * ch3: (DEF = 1 A)
        * The command is combination of INSTrument:SELect, [SOURce:]CURRent, and [SOURce:]VOLTage commands.
            The values of the voltage and current of the specified output will change as soon as the command is executed.
        * You can identify each output by the output name (P6V, P25V, N25V, ch1, ch2, or ch3).
            For the voltage and current parameters of the APPLy command, the ranges depend on the output currently selected.
            You can substitute “MINimum”, “MAXimum”, or “DEFault” in place of a specific value for the voltage and current parameters.
            If you specify only one value for the parameter, the power supply regards it as voltage setting value.
            If you do not specify any value for the parameter, the APPLy command only selects the output specified
            and acts as the INSTrument command.
        * example:
            Sets the maximum voltage and current of +6 V output : APPL P6V, MAX, MAX
        """
        cmd = "APPL {}, {}, {}".format(channel, voltage, current)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def dcp_device_specify_channel_output_voltage_and_current_level(self, channel: str):
        """
        * Full cmd format: "APPLy? <channel>"
        * Short cmd format: "APPL? <channel>"
        * <channel>:
            * P6V or CH1
            * P25V or CH2
            * N25V or CH3
        * The query returns the power supply's present voltage and current values
            for each output as a quoted string as shown in the sample string below
            (the quotation marks are returned as part of the string).
            If any output identifier is not specified, the voltage and the current
            of the currently selected output are returned.
        * example:
            * return: "5.000000,1.000000"
            * Referring to the above string, the first number 5.000000 is the voltage limit value
            and the second number 1.000000 is the current limit value for the specified output.
        """
        cmd = "APPL? {}".format(channel)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.01)
        ret, data = self.recv_data()
        print(data)