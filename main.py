#!/usr/bin/env python3

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt
from gxpy.gxengine.eventengine import EventEngine
from gxpy.gxengine.mainengine import MainEngine
from logging import CRITICAL, ERROR, WARN, INFO, DEBUG
try:
    from gxpy.gxengine.utility import create_qapp
except ImportError:
    # 如果gxpy模块不可用，使用本地实现
    def create_qapp(app_ico=None):
        app = QApplication(sys.argv)
        if app_ico:
            app.setWindowIcon(QIcon(app_ico))
        # macOS特殊处理
        if sys.platform == 'darwin':
            app.setAttribute(Qt.AA_DontShowIconsInMenus, False)
            app.setAttribute(Qt.AA_NativeWindows, True)
        return app
from gxpy.gxengine.common import resource_path
from gxpy.gxengine.uartmanageengine import UartManageEngine
from gxpy.gxengine.uartmsgengine import UartMsgEngine
from gxpy.gxengine.emailengine import EmailEngine
from gxapp.bttengine import BttEngine
from gxapp.mainwindow import MainWindow
from gxapp.logengine import LogEngine


def main():
    """Start app"""
    qapp = create_qapp(app_ico=resource_path("gxapp/ui/chip.ico"))

    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    log_param = {
            "log_level": DEBUG,
            "log_console": True,
            "log_file": True,
            "log_file_dir": "log",
            }
    main_engine.add_engine(LogEngine, log_param)
    email_param = {
            "email_server": "smtp.exmail.qq.com",
            "email_username": "<EMAIL>",
            "email_password": "Ptd@88156088",
            }
    main_engine.add_engine(EmailEngine, email_param)
    main_engine.add_engine(BttEngine)
    main_engine.add_engine(UartMsgEngine)
    main_window = MainWindow(main_engine, event_engine)
    main_window.show()

    qapp.exec()


if __name__ == "__main__":
    main()
