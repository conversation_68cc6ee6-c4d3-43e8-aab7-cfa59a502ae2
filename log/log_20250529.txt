2025-05-29 09:08:35,928  INFO: 测试开始!
2025-05-29 09:08:35,930  INFO: 测试进度：0/1
2025-05-29 09:08:35,930  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 09:08:35,931  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 09:08:35,955  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-29 09:09:53,147  INFO: 测试开始!
2025-05-29 09:09:53,148  INFO: 测试进度：0/1
2025-05-29 09:09:53,148  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 09:09:53,148  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 09:09:53,159  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-29 09:10:49,476  INFO: 测试开始!
2025-05-29 09:10:49,477  INFO: 测试进度：0/1
2025-05-29 09:10:49,477  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 09:10:49,478  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 09:10:49,538  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 09:10:49,564  INFO: 开始测试
2025-05-29 09:10:52,685  INFO: Generator State: OFF
2025-05-29 09:10:53,494  INFO: level: INV
2025-05-29 09:11:04,481  INFO: 测试结束
2025-05-29 09:11:04,483  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 09:11:04,483  INFO: 测试进度：1/1
2025-05-29 09:11:09,486  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 09:11:09,488  INFO: 测试完成！总共测试耗时：00:00:20
2025-05-29 11:32:18,928  INFO: 测试开始!
2025-05-29 11:32:18,929  INFO: 测试进度：0/1
2025-05-29 11:32:18,929  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 11:32:18,929  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 11:32:18,940  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-29 11:32:51,016  INFO: 测试开始!
2025-05-29 11:32:51,018  INFO: 测试进度：0/1
2025-05-29 11:32:51,018  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 11:32:51,018  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 11:32:51,037  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-29 11:33:15,383  INFO: 测试开始!
2025-05-29 11:33:15,384  INFO: 测试进度：0/1
2025-05-29 11:33:15,384  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 11:33:15,384  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 11:33:15,467  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 11:33:15,493  INFO: 开始测试
2025-05-29 11:33:15,705  INFO: Generator State: OFF
2025-05-29 11:33:16,524  INFO: level: INV
2025-05-29 11:33:27,485  INFO: 测试结束
2025-05-29 11:33:27,487  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 11:33:27,488  INFO: 测试进度：1/1
2025-05-29 11:33:32,492  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 11:33:32,494  INFO: 测试完成！总共测试耗时：00:00:17
2025-05-29 13:24:48,616  INFO: 测试开始!
2025-05-29 13:24:48,617  INFO: 测试进度：0/1
2025-05-29 13:24:48,618  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:24:48,618  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:24:48,717  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:24:48,735  INFO: 开始测试
2025-05-29 13:24:51,854  INFO: Generator State: OFF
2025-05-29 13:24:52,660  INFO: level: INV
2025-05-29 13:24:54,098  INFO: 测试结束
2025-05-29 13:24:54,101  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:24:54,101  INFO: 测试进度：1/1
2025-05-29 13:24:59,102  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:24:59,104  INFO: 测试完成！总共测试耗时：00:00:11
2025-05-29 13:25:28,312  INFO: 测试开始!
2025-05-29 13:25:28,313  INFO: 测试进度：0/1
2025-05-29 13:25:28,313  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:25:28,313  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:25:28,400  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:25:28,423  INFO: 开始测试
2025-05-29 13:25:28,629  INFO: Generator State: OFF
2025-05-29 13:25:29,439  INFO: level: INV
2025-05-29 13:25:40,413  INFO: 测试结束
2025-05-29 13:25:40,415  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:25:40,415  INFO: 测试进度：1/1
2025-05-29 13:25:45,418  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:25:45,420  INFO: 测试完成！总共测试耗时：00:00:17
2025-05-29 13:26:52,816  INFO: 测试开始!
2025-05-29 13:26:52,817  INFO: 测试进度：0/1
2025-05-29 13:26:52,817  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:26:52,817  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:26:52,901  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:26:52,923  INFO: 开始测试
2025-05-29 13:26:53,135  INFO: Generator State: OFF
2025-05-29 13:26:53,945  INFO: level: INV
2025-05-29 13:26:54,344  INFO: 测试结束
2025-05-29 13:26:54,346  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:26:54,347  INFO: 测试进度：1/1
2025-05-29 13:26:59,350  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:26:59,354  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-29 13:28:25,303  INFO: 测试开始!
2025-05-29 13:28:25,304  INFO: 测试进度：0/1
2025-05-29 13:28:25,304  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:28:25,305  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:28:25,391  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:28:25,413  INFO: 开始测试
2025-05-29 13:28:25,622  INFO: Generator State: OFF
2025-05-29 13:28:26,431  INFO: level: INV
2025-05-29 13:28:28,382  INFO: 测试结束
2025-05-29 13:28:28,385  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:28:28,385  INFO: 测试进度：1/1
2025-05-29 13:28:33,387  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:28:33,389  INFO: 测试完成！总共测试耗时：00:00:08
2025-05-29 13:29:03,118  INFO: 测试开始!
2025-05-29 13:29:03,120  INFO: 测试进度：0/1
2025-05-29 13:29:03,120  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:29:03,120  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:29:03,207  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:29:03,228  INFO: 开始测试
2025-05-29 13:29:03,440  INFO: Generator State: OFF
2025-05-29 13:29:04,250  INFO: level: INV
2025-05-29 13:29:06,196  INFO: 测试结束
2025-05-29 13:29:06,198  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:29:06,198  INFO: 测试进度：1/1
2025-05-29 13:29:11,203  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:29:11,206  INFO: 测试完成！总共测试耗时：00:00:08
2025-05-29 13:30:22,349  INFO: 测试开始!
2025-05-29 13:30:22,350  INFO: 测试进度：0/1
2025-05-29 13:30:22,351  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:30:22,351  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:30:22,436  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:30:22,459  INFO: 开始测试
2025-05-29 13:30:22,667  INFO: Generator State: OFF
2025-05-29 13:30:23,476  INFO: level: INV
2025-05-29 13:30:24,904  INFO: 测试结束
2025-05-29 13:30:24,907  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:30:24,913  INFO: 测试进度：1/1
2025-05-29 13:30:29,911  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:30:29,913  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-29 13:31:30,957  INFO: 测试开始!
2025-05-29 13:31:30,958  INFO: 测试进度：0/1
2025-05-29 13:31:30,958  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:31:30,958  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:31:31,042  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:31:31,068  INFO: 开始测试
2025-05-29 13:31:31,274  INFO: Generator State: OFF
2025-05-29 13:31:32,083  INFO: level: INV
2025-05-29 13:31:33,486  INFO: 测试结束
2025-05-29 13:31:33,488  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:31:33,488  INFO: 测试进度：1/1
2025-05-29 13:31:38,490  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:31:38,492  INFO: 测试完成！总共测试耗时：00:00:08
2025-05-29 13:32:28,356  INFO: 测试开始!
2025-05-29 13:32:28,357  INFO: 测试进度：0/1
2025-05-29 13:32:28,358  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:32:28,358  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:32:28,446  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:32:28,460  ERROR: uart connect error
2025-05-29 13:32:28,462  ERROR: 测试终止！测试错误：uart connect error
2025-05-29 13:32:37,204  INFO: 测试开始!
2025-05-29 13:32:37,205  INFO: 测试进度：0/1
2025-05-29 13:32:37,205  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:32:37,205  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:32:37,284  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:32:37,308  INFO: 开始测试
2025-05-29 13:32:37,511  INFO: Generator State: OFF
2025-05-29 13:32:38,321  INFO: level: INV
2025-05-29 13:32:38,921  INFO: 测试结束
2025-05-29 13:32:38,923  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:32:38,923  INFO: 测试进度：1/1
2025-05-29 13:32:43,926  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:32:43,928  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-29 13:34:39,115  INFO: 测试开始!
2025-05-29 13:34:39,116  INFO: 测试进度：0/1
2025-05-29 13:34:39,116  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:34:39,116  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:34:39,201  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:34:39,226  INFO: 开始测试
2025-05-29 13:34:39,447  INFO: Generator State: OFF
2025-05-29 13:34:40,297  INFO: level: INV
2025-05-29 13:34:40,921  INFO: 测试结束
2025-05-29 13:34:40,924  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 13:34:40,924  INFO: 测试进度：1/1
2025-05-29 13:34:45,928  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 13:34:45,931  INFO: 测试完成！总共测试耗时：00:00:07
2025-05-29 13:37:53,561  INFO: 测试开始!
2025-05-29 13:37:53,563  INFO: 测试进度：0/1
2025-05-29 13:37:53,563  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:37:53,563  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:37:53,649  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:37:53,674  INFO: 开始测试
2025-05-29 13:37:53,896  ERROR: 测试终止！测试错误：测试用例执行异常( 'tuple' object has no attribute 'find' )
2025-05-29 13:45:32,918  INFO: 测试开始!
2025-05-29 13:45:32,919  INFO: 测试进度：0/1
2025-05-29 13:45:32,919  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:45:32,920  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:45:33,003  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:45:33,029  INFO: 开始测试
2025-05-29 13:45:33,237  ERROR: 测试终止！测试错误：测试用例执行异常( bytearray index out of range )
2025-05-29 13:48:26,959  INFO: 测试开始!
2025-05-29 13:48:26,960  INFO: 测试进度：0/1
2025-05-29 13:48:26,961  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:48:26,961  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:48:27,045  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:48:27,065  INFO: 开始测试
2025-05-29 13:48:27,378  ERROR: 测试终止！测试错误：测试用例执行异常( 'list' object has no attribute 'find' )
2025-05-29 13:55:40,018  INFO: 测试开始!
2025-05-29 13:55:40,019  INFO: 测试进度：0/1
2025-05-29 13:55:40,019  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 13:55:40,020  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 13:55:40,104  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 13:55:40,122  INFO: 开始测试
2025-05-29 13:55:40,341  ERROR: 测试终止！测试错误：测试用例执行异常( can't concat list to bytearray )
2025-05-29 14:01:25,667  INFO: 测试开始!
2025-05-29 14:01:25,668  INFO: 测试进度：0/1
2025-05-29 14:01:25,668  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:01:25,668  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:01:25,752  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:01:25,779  INFO: 开始测试
2025-05-29 14:01:26,086  INFO: 测试结束
2025-05-29 14:01:26,087  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 14:01:26,088  INFO: 测试进度：1/1
2025-05-29 14:01:31,092  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 14:01:31,093  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-29 14:03:59,827  INFO: 测试开始!
2025-05-29 14:03:59,828  INFO: 测试进度：0/1
2025-05-29 14:03:59,828  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:03:59,829  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:03:59,913  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:03:59,942  INFO: 开始测试
2025-05-29 14:04:00,252  ERROR: 测试终止！测试错误：测试用例执行异常( 'list' object has no attribute 'find' )
2025-05-29 14:08:53,864  INFO: 测试开始!
2025-05-29 14:08:53,865  INFO: 测试进度：0/1
2025-05-29 14:08:53,865  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:08:53,866  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:08:53,949  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:08:53,974  INFO: 开始测试
2025-05-29 14:08:54,283  INFO: 测试结束
2025-05-29 14:08:54,286  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 14:08:54,286  INFO: 测试进度：1/1
2025-05-29 14:08:59,290  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 14:08:59,292  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-29 14:13:00,873  INFO: 测试开始!
2025-05-29 14:13:00,874  INFO: 测试进度：0/1
2025-05-29 14:13:00,874  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:13:00,875  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:13:00,960  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:13:00,987  INFO: 开始测试
2025-05-29 14:13:01,623  INFO: 测试结束
2025-05-29 14:13:01,625  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 14:13:01,625  INFO: 测试进度：1/1
2025-05-29 14:13:06,630  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 14:13:06,632  INFO: 测试完成！总共测试耗时：00:00:06
2025-05-29 14:15:06,322  INFO: 测试开始!
2025-05-29 14:15:06,323  INFO: 测试进度：0/1
2025-05-29 14:15:06,324  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:15:06,324  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:15:06,409  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:15:06,430  INFO: 开始测试
2025-05-29 14:15:07,063  INFO: 测试结束
2025-05-29 14:15:07,066  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 14:15:07,066  INFO: 测试进度：1/1
2025-05-29 14:15:12,070  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 14:15:12,072  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-29 14:16:02,512  INFO: 测试开始!
2025-05-29 14:16:02,513  INFO: 测试进度：0/1
2025-05-29 14:16:02,513  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:16:02,514  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:16:02,599  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:16:02,619  INFO: 开始测试
2025-05-29 14:16:03,259  INFO: 测试结束
2025-05-29 14:16:03,261  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 14:16:03,261  INFO: 测试进度：1/1
2025-05-29 14:16:08,266  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 14:16:08,269  INFO: 测试完成！总共测试耗时：00:00:05
2025-05-29 14:19:37,212  INFO: 测试开始!
2025-05-29 14:19:37,213  INFO: 测试进度：0/1
2025-05-29 14:19:37,213  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-29 14:19:37,213  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-29 14:19:37,304  INFO: bluetooth_tester host_id: Rohde&Schwarz,CMW,1201.0002k75/101147,3.7.171
2025-05-29 14:19:37,329  INFO: 开始测试
2025-05-29 14:19:38,293  INFO: 测试结束
2025-05-29 14:19:38,295  INFO: 当前用例 superlink_rx_gauss 执行完成！
2025-05-29 14:19:38,295  INFO: 测试进度：1/1
2025-05-29 14:19:43,299  INFO: （蓝牙指标测试）用例集的用例执行结束！
2025-05-29 14:19:43,301  INFO: 测试完成！总共测试耗时：00:00:06
