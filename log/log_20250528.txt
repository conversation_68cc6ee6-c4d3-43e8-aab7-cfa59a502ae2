2025-05-28 20:32:44,243  INFO: 测试开始!
2025-05-28 20:32:44,244  INFO: 测试进度：0/1
2025-05-28 20:32:44,244  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-28 20:32:44,245  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-28 20:32:44,279  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-28 20:35:51,094  INFO: 测试开始!
2025-05-28 20:35:51,095  INFO: 测试进度：0/1
2025-05-28 20:35:51,095  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-28 20:35:51,095  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-28 20:35:51,104  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-28 20:36:45,626  INFO: 测试开始!
2025-05-28 20:36:45,627  INFO: 测试进度：0/1
2025-05-28 20:36:45,627  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-28 20:36:45,627  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-28 20:36:45,640  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
2025-05-28 20:37:44,326  INFO: 测试开始!
2025-05-28 20:37:44,328  INFO: 测试进度：0/1
2025-05-28 20:37:44,329  INFO: （蓝牙指标测试）用例集的用例执行开始
2025-05-28 20:37:44,329  INFO: 当前执行的用例为：superlink_rx_gauss
2025-05-28 20:37:44,346  ERROR: 测试终止！测试错误：测试用例执行异常( (unicode error) 'unicodeescape' codec can't decode bytes in position 209-210: truncated \UXXXXXXXX escape (cmw_device_api.py, line 244) )
