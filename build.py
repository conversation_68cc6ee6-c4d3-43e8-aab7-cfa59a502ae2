import sys
import os
import subprocess
import codecs
from gxapp.version import version


def generate_version_file():
    version_file_str = """
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 0, 0, 1),
    prodvers=(1, 0, 0, 1),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904e4',
        [StringStruct(u'CompanyName', u'杭州国芯科技股份有限公司'),
        StringStruct(u'FileDescription', u'工具'),
        StringStruct(u'FileVersion', u'%s'),
        StringStruct(u'InternalName', u'www.nationalchip.com'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2001-2021 NationalChip All Rights Reserved'),
        StringStruct(u'OriginalFilename', u'工具'),
        StringStruct(u'ProductName', u'工具'),
        StringStruct(u'ProductVersion', u'%s')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1252])])
  ]
)""" % (version,version)
    f = codecs.open('version.txt', 'w', 'utf-8')
    f.write(version_file_str)
    f.close()


def run_cmd(full_cmd):
    print(full_cmd)
    subprocess.call(full_cmd, shell=True)


if __name__ == '__main__':
    app_name = "gxbtt"
    dir_name = app_name + "_v" + version
    generate_version_file()
    tc_set_name = "testcases"
    module_set_name = "module_set"

    if sys.platform == 'win32':     # windows
        # run_cmd("pyinstaller  --clean  -F -w                 \
        #                     --icon=gxapp/ui/chip.ico         \
        #                     --add-data=\"gxapp/ui;gxapp/ui\" \
        #                     --add-data=\"bin;bin\"           \
        #                     -n=gxbtt                         \
        #                     --version-file=version.txt main.py")
        run_cmd(
            "pyinstaller  --clean  -F  "
            "--icon=gxapp/ui/chip.ico "
            "--add-data=\"gxapp/ui;gxapp/ui\" "
            "--add-data=\"bin;bin\"  "
            # "--hidden-import=module_set.device_run_func_class "
            # "--hidden-import=module_set.draw_class "
            # "--hidden-import=module_set.handle_report_class "
            "-n={} "
            "--version-file=version.txt main.py".format(app_name)
        )

        run_cmd("mkdir " + dir_name)
        run_cmd("copy dist " + dir_name)
        run_cmd("copy config.ini %s" % dir_name)
        run_cmd("xcopy {} {} /E /I".format(tc_set_name, os.path.join(dir_name, tc_set_name)))
        run_cmd("xcopy {} {} /E /I".format(module_set_name, os.path.join(dir_name, module_set_name)))
        run_cmd("del /F /S /Q version.txt")
        run_cmd("del /F /S /Q __pycache__ build dist {}.spec".format(app_name))
        run_cmd("rd /S /Q __pycache__ build dist")

    elif sys.platform == 'linux':   # linux
        # run_cmd("pyinstaller  --clean  -F -w                 \
        #                     --icon ui/chip.ico               \
        #                     --add-data \"gxapp/ui:gxapp/ui\" \
        #                     -n gxbtt.elf                     \
        #                     --version-file version.txt main.py")
        run_cmd(
            r"pyinstaller  --clean  -F -w "
            "--icon ui/chip.ico "
            "--add-data 'gxapp/ui:gxapp/ui' "
            # "--hidden-import 'module_set.device_run_func_class' "
            # "--hidden-import 'module_set.draw_class' "
            # "--hidden-import 'module_set.handle_report_class' "
            "-n {}.elf "
            "--version-file version.txt main.py".format(app_name)
        )
        run_cmd("mkdir " + dir_name)
        run_cmd("cp dist/* " + dir_name)
        run_cmd("cp config.ini " + dir_name)
        run_cmd("cp -r {} {}".format(tc_set_name, dir_name))
        run_cmd("cp -r {} {}".format(module_set_name, dir_name))
        run_cmd("rm version.txt")
        run_cmd("rm -rf __pycache__ build dist {}.elf.spec".format(app_name))

    elif sys.platform == 'darwin':  # Mac OS
        pass
    else:
        print("The system is not supported!")
