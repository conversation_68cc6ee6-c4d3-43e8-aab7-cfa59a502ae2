import platform
import time
import os
import sys
import collections

from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import Device<PERSON>un
from module_set.draw_class import Mat<PERSON><PERSON>libPlotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data

        self.select_sa = True
        self.select_vsg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 创建报告文件
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        # ret, report_path, error = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        if ret is False:
            return ret, error
        else:
            self.tc_report_path = report_path_info["tc_report_path"]
            self.tc_report_dir_path = report_path_info["tc_report_dir_path"]
            self.tc_set_report_path = report_path_info["tc_set_report_path"]
            self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")
        print(self.data)
        print(__file__)

        self.sa_api.sa_device_set_full_mode_preset()
        time.sleep(1)
        screen_list = self.sa_api.sa_device_query_screen_list()
        if len(screen_list) > 1:
            self.sa_api.sa_device_del_all_screen_except_current_screen()
        self.sa_api.sa_device_select_measurement_application_by_mode_name(mode_name="SA")
        self.sa_api.sa_device_set_specified_measurement(measurement="SAN")

        ret = self.sa_api.sa_device_query_current_measurement_name()
        self.write_info_log(msg=ret)

        ##  DUT进入发射模式，配置频点（例如2402MHz），发射功率配置最大
        start_fre = 2402
        end_fre = 2480

        power_cfg_dict = collections.OrderedDict({
            '0x00': -19.9, '0x01': -19.2, '0x02': -18.3, '0x03': -17.4, '0x04': -16.5, '0x05': -15.6,
            '0x06': -14.8, '0x07': -13.8, '0x08': -12.9, '0x09': -12.1, '0x0a': -11.2, '0x0b': -10.4,
            '0x0c': -9.4, '0x0d': -8.5, '0x0e': -7.7, '0x0f': -6.8, '0x10': -5.9, '0x11': -5.1, '0x12': -4.3,
            '0x13': -3.4, '0x14': -2.6, '0x15': -1.9, '0x16': -1.2, '0x17': -0.8, '0x18': -0.6, '0x19': -0.4})

        power_cfg_dict_sort = collections.OrderedDict(sorted(power_cfg_dict.items(), key=lambda x: x[0], reverse=True))
        for fre in range(start_fre, end_fre + 1, 78):
            report_sheet_name = "频道号rf_ch_{}".format(fre)

            self.sa_api.sa_device_set_center_frequency(freq=fre, unit="MHz")  # 设置center frequency
            self.sa_api.sa_device_set_span_value(freq=10, unit="MHz")  # 设置span

            self.sa_api.sa_device_set_res_bw_auto_function(function="ON")  # 设置res bw为auto时，bw fre默认为91kHz
            # self.sa_api.sa_device_set_res_bw_freq(freq=91, unit="kHz")

            self.sa_api.sa_device_set_reference_level(ref_level=23, unit="dBm")  # 设置ref_level
            self.sa_api.sa_device_set_scale_div(rel_ampl=10, unit="dB")  # 设置scale/div

            single_rf_ch_result = list()
            for step, power in power_cfg_dict.items():
                self.write_info_log("power: {}".format(power))
                self.vsg_api.vsg_device_set_output_frequency(value=fre, unit="MHz", mode="CW")
                self.vsg_api.vsg_device_set_rf_output_power(value=power, unit="DBM")
                if platform.version() == "Windows":
                    time.sleep(0.1)
                self.sa_api.sa_device_set_peak_search_mode(mode="MAX")  # 设置peak search的模式为MAX
                self.sa_api.sa_device_set_peak_search_by_marker_number(numb=1)  # 设置peak search，using marker <numb>，默认是1
                self.sa_api.sa_device_marker_set_marker_table_state(state="OFF")
                self.sa_api.sa_device_swept_sa_measurement_set_trace_type(
                    trace_type="AVERage", trace_numb=1)  # swept_sa meas下 Trace Type 选择 Trace Average
                time.sleep(2)
                power_extreme = self.sa_api.sa_device_query_marker_frequency_or_time_x_axis_value_by_marker_number(
                    numb=1)  # marker1 的功率极值
                self.write_info_log("power_extreme: {}MHz".format(float(power_extreme) / 1E+06))
                signal_strength = self.sa_api.sa_device_query_marker_amplitude_y_axis_value_by_marker_number(
                    numb=1)  # 记录 marker1 的信号强度
                self.write_info_log("signal_strength: {}dBm".format(float(signal_strength)))

                # 截屏并保存
                image_dir_path = os.path.join(self.tc_report_dir_path, report_sheet_name)
                image_name = "{}_pa_gain_{}_captured_screen_image.png".format(report_sheet_name, step)
                self.write_info_log("截屏保存中...")
                self.sa_api.sa_func_save_screenshot_image(
                    image_dir_path=image_dir_path, image_name=image_name)
                self.write_info_log("截屏保存结束，图片名称为：{}".format(image_name))
                self.sa_api.sa_device_swept_sa_measurement_set_trace_type(
                    trace_type="WRITe", trace_numb=1)  # swept_sa meas下 Trace Type 选择 Trace WRITe

                pa_gain = step
                tx_power = float(signal_strength)
                if list(power_cfg_dict.items()).index((step, power)) == 0:
                    power_step = "N/A"
                    single_pa_gain_result = (pa_gain, tx_power, power_step)
                    single_rf_ch_result.append(single_pa_gain_result)
                    report_title_0 = ["", report_sheet_name, ""]
                    report_title_1 = ["PA gain", "TX Power(dBm)", "Power Step(dB)"]
                    self.handle_report.write_report_title_data(sheet_name=report_sheet_name, data=report_title_0)
                    self.handle_report.write_report_title_data(sheet_name=report_sheet_name, data=report_title_1)
                    self.handle_report.write_report_test_data(sheet_name=report_sheet_name, data=single_pa_gain_result)
                else:
                    power_step = tx_power - single_rf_ch_result[-1][1]
                    single_pa_gain_result = (pa_gain, tx_power, power_step)
                    single_rf_ch_result.append(single_pa_gain_result)
                    self.handle_report.write_report_test_data(sheet_name=report_sheet_name, data=single_pa_gain_result)

            # 画图
            x_axis_data = [int(item[0], 16) for item in single_rf_ch_result]
            y_axis_data = [item[1] for item in single_rf_ch_result]
            mp = MatplotlibPlotter(x_axis_data, y_axis_data)
            pic_buf = mp.draw_single_line_graph(
                title="发射功率测试_频道号rf_ch_{}".format(fre), x_label="配置档位", y_label="发射功率")
            # pic_buf = mp.plot_bar_chart(
            #     title="发射功率测试_频道号rf_ch_{}".format(fre), x_label="配置档位", y_label="发射功率")
            self.handle_report.write_report_image_data(sheet_name=report_sheet_name, image_data=pic_buf)
        self.write_info_log(msg="测试结束")


if __name__ == "__main__":
    from gxapp.public_data import test_data

    # 获取当前文件的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(current_dir)

    event_engine = EventEngine()
    case_run = CaseRun(data=test_data, event_engine=event_engine)
    case_run.run()
