import re
import time
import os
import sys

from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import Device<PERSON>un
from module_set.draw_class import <PERSON><PERSON><PERSON><PERSON>b<PERSON>lotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        # self.select_dcp = True
        self.select_sa = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        # 配置直流电源
        # # 指定电源的工作模式
        # self.dcp_api.dcp_device_set_power_supply_operation_mode(mode="OFF")  # 设置非并联，非串联的电源模式
        # # 打开前面板显示屏
        # self.dcp_api.dcp_device_set_display_state(state="ON")
        # # 启用指定通道输出
        # self.dcp_api.dcp_device_set_specify_channel_output_state(state="ON", chanlist="1")
        # # 选择通道仪表视图
        # self.dcp_api.dcp_device_set_channel_meter_view_mode(mode="METER1")
        # # 同时设置电压和电流
        # self.dcp_api.dcp_device_set_specify_channel_output_voltage_and_current_level(
        #     channel="CH1", voltage=3, current=1)

        self.sa_api.sa_device_set_full_mode_preset()
        time.sleep(1)
        screen_list = self.sa_api.sa_device_query_screen_list()
        if len(screen_list) > 1:
            self.sa_api.sa_device_del_all_screen_except_current_screen()
        self.sa_api.sa_device_select_measurement_application_by_mode_name(mode_name="SA")
        self.sa_api.sa_device_set_specified_measurement(measurement="SAN")

        ret = self.sa_api.sa_device_query_current_measurement_name()
        self.write_info_log(msg=ret)

        # TODO DUT进入发射模式，配置频点（例如2402MHz），发射功率配置最大

        self.sa_api.sa_device_set_center_frequency(freq=2402, unit="MHz")             # 设置center frequency
        self.sa_api.sa_device_set_span_value(freq=10, unit="MHz")                     # 设置span
        self.sa_api.sa_device_swept_sa_measurement_set_trace_type(
            trace_type="AVERage", trace_numb=1)     # swept_sa meas下 Trace Type 选择 Trace Average
        self.sa_api.sa_device_set_res_bw_auto_function(function="ON")                 # 设置res bw为auto时，bw fre默认为91kHz
        # self.sa_api.sa_device_set_res_bw_freq(freq=91, unit="kHz")

        self.sa_api.sa_device_set_reference_level(ref_level=23, unit="dBm")           # 设置ref_level
        self.sa_api.sa_device_set_scale_div(rel_ampl=10, unit="dB")                   # 设置scale/div

        self.sa_api.sa_device_set_peak_search_mode(mode="MAX")                        # 设置peak search的模式为MAX
        self.sa_api.sa_device_set_peak_search_by_marker_number(numb=1)                # 设置peak search，using marker <numb>，默认是1
        self.sa_api.sa_device_marker_set_marker_table_state(state="OFF")
        power_extreme = self.sa_api.sa_device_query_marker_frequency_or_time_x_axis_value_by_marker_number(numb=1)  # marker1 的功率极值
        self.write_info_log("power_extreme: {}MHz".format(float(power_extreme) / 1E+06))
        signal_strength = self.sa_api.sa_device_query_marker_amplitude_y_axis_value_by_marker_number(numb=1)    # 记录 marker1 的信号强度
        self.write_info_log("signal_strength: {}dBm".format(float(signal_strength)))

        # self.sa_api.sa_device_store_current_screen_image(filename="myScreen.png")     # 将当前屏幕截图保存

        self.write_info_log(msg="测试结束")


if __name__ == "__main__":
    from gxapp.public_data import test_data

    # 获取当前文件的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(current_dir)

    event_engine = EventEngine()
    case_run = CaseRun(data=test_data, event_engine=event_engine)
    case_run.run()
