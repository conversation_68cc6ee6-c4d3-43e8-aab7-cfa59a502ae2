import time
import os
import sys

from module_set.device_run_func_class import <PERSON><PERSON><PERSON><PERSON>
from gxapp.logengine import LogD<PERSON>, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.select_exg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")
        # self.exg_api.exg_device_set_preset()
        # time.sleep(5)

        self.exg_api.exg_device_set_output_frequency(value=2402, unit="MHz", mode="CW")
        self.exg_api.exg_device_query_output_frequency(mode="CW")
        self.exg_api.exg_device_query_rf_output_power()
        self.exg_api.exg_device_query_rf_output_power_unit()
        self.exg_api.exg_device_set_rf_output_power(value=-70, unit="DBM")

        self.exg_api.exg_device_set_rf_output_power_unit(unit="DBM")
        self.exg_api.exg_device_query_rf_output_power()
        self.exg_api.exg_device_query_rf_output_power_unit()

        self.exg_api.exg_device_query_rf_output_state()
        self.exg_api.exg_device_set_rf_output_state(state="ON")
        self.exg_api.exg_device_query_rf_output_state()

        self.exg_api.exg_device_query_rf_output_modulation_state()
        self.exg_api.exg_device_set_rf_output_modulation_state(state="ON")
        self.exg_api.exg_device_query_rf_output_modulation_state()

        self.exg_api.exg_device_query_sweep_type()
        self.exg_api.exg_device_set_sweep_type(value="STEP")
        self.exg_api.exg_device_query_sweep_type()

        self.exg_api.exg_device_query_step_sweep_start_frequency()
        self.exg_api.exg_device_set_step_sweep_start_frequency(value=2.402, unit="GHz")
        self.exg_api.exg_device_query_step_sweep_start_frequency()

        self.exg_api.exg_device_query_step_sweep_stop_frequency()
        self.exg_api.exg_device_set_step_sweep_stop_frequency(value=2.412, unit="GHz")
        self.exg_api.exg_device_query_step_sweep_stop_frequency()

        self.exg_api.exg_device_query_sweep_points()
        self.exg_api.exg_device_set_sweep_points(value=1000)
        self.exg_api.exg_device_query_sweep_points()

        # self.exg_api.exg_device_query_sweep_trigger_source()
        # self.exg_api.exg_device_set_sweep_trigger_source(source="IMM")
        # self.exg_api.exg_device_query_sweep_trigger_source()
        #
        # self.exg_api.exg_device_query_select_sweep_repeat_single_cont()
        # self.exg_api.exg_device_set_select_sweep_repeat_single_cont(value="OFF")
        # self.exg_api.exg_device_query_select_sweep_repeat_single_cont()
        # self.exg_api.exg_device_set_and_start_single_sweep()

        # Cont. Sweep Remote stepped sweep on
        # self.exg_api.exg_device_set_select_sweep_repeat_single_cont(value="ON")
        # self.exg_api.exg_device_set_current_sweep_operate_mode(mode="AUTO")
        # self.exg_api.exg_device_set_sweep_point_dwell_time_type(value="STEP")
        # self.exg_api.exg_device_set_sweep_type(value="STEP")
        # self.exg_api.exg_device_set_frequency_mode(mode="LIST")
        # self.exg_api.exg_device_set_sweep_event_point_trigger_source(source="IMM")
        # self.exg_api.exg_device_execute_remote_stepped_sweep()
        # time.sleep(30)
        # # Remote stepped sweep off
        # self.exg_api.exg_device_set_frequency_mode(mode="CW")

        # Single Sweep mode on
        self.exg_api.exg_device_set_select_sweep_repeat_single_cont(value="OFF")
        self.exg_api.exg_device_set_current_sweep_operate_mode(mode="AUTO")
        self.exg_api.exg_device_set_sweep_event_point_trigger_source(source="IMM")
        self.exg_api.exg_device_set_sweep_point_dwell_time_type(value="STEP")
        self.exg_api.exg_device_set_sweep_type(value="STEP")
        self.exg_api.exg_device_set_frequency_mode(mode="LIST")
        self.exg_api.exg_device_set_and_start_single_sweep()
        time.sleep(30)

        # self.exg_api.exg_device_query_frequency_mode()
        # self.exg_api.exg_device_query_signal_generator_power_mode()
        # self.exg_api.exg_device_set_signal_generator_power_mode(mode="LIST")  # 开始Ampl Sweep
        # self.exg_api.exg_device_query_signal_generator_power_mode()
        # self.exg_api.exg_device_set_frequency_mode(mode="LIST")     # 开始Fre Sweep
        # self.exg_api.exg_device_query_frequency_mode()
        self.write_info_log(msg="测试结束")


if __name__ == "__main__":
    from gxapp.public_data import test_data
    # 获取当前文件的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(current_dir)

    event_engine = EventEngine()
    case_run = CaseRun(data=test_data, event_engine=event_engine)
    case_run.run()
