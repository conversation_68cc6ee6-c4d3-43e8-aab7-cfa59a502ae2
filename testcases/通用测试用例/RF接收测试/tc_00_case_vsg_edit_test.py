import time
import os
import sys
import numpy as np
import importlib

from module_set.device_run_func_class import Device<PERSON>un

from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.draw_class import MatplotlibPlotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.select_exg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")
        # self.exg_api.exg_device_set_preset()
        # time.sleep(5)

        print("line_loss: {}".format(self.line_loss))
        self.write_error_log(msg="line_loss: {}".format(self.line_loss))
        # self.write_error_log(msg="save_report_path: {}".format(self.save_report_path))

        self.print_something()
        self.print_other_something()
        self.exg_api.exg_device_print_something()
        self.exg_api.exg_device_func_print_something()
        self.exg_api.exg_device_func_print_other_something()

        x = np.linspace(0, 2 * np.pi, 50)
        x_data = list(x)
        y = np.sin(x)
        y1 = np.cos(x)
        z = list()
        z.append(y)
        z.append(y1)
        z = list(map(lambda k: k.tolist(), z))

        draw = MatplotlibPlotter(x_axis_data=x_data, y_axis_data=z)
        draw_buffer = draw.plot_line_chart(title="曲线图示例", x_label="x轴", y_label="y轴")

        report_name = "exg_report.xlsx"
        if self.save_report_path is None:
            report_path = "./cli_report/{}".format(report_name)
        else:
            report_path = "{}".format(os.path.join(self.save_report_path, report_name))
        handle = HandleExcelReport(report_path)
        handle.write_report_image_data(sheet_name="image_report", image_data=draw_buffer, interval_row=1)
        title = ["Frequency (MHz)", "Power (dBm)"]
        test_data = [0.1, 2]
        handle.write_report_title_data(sheet_name="test_data", data=title)
        handle.write_report_test_data(sheet_name="test_data", data=test_data)

        self.write_info_log(msg="测试结束")


if __name__ == "__main__":
    from gxapp.public_data import test_data

    # 获取当前文件的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(current_dir)

    event_engine = EventEngine()
    case_run = CaseRun(data=test_data, event_engine=event_engine)
    case_run.run()
