import time
import os
import sys
import numpy as np
from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import <PERSON><PERSON><PERSON><PERSON>
from module_set.draw_class import Mat<PERSON>lotlibPlotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data

        # self.select_dcp = True
        self.select_sa = True
        self.select_vsg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 创建报告文件
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        if ret is False:
            return ret, error
        else:
            self.tc_name = report_path_info["case_name"]
            self.tc_report_path = report_path_info["tc_report_path"]
            self.tc_report_dir_path = report_path_info["tc_report_dir_path"]
            self.tc_set_report_path = report_path_info["tc_set_report_path"]
            self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        # # 配置直流电源
        # self.dcp_api.device_set_reset()
        # # 指定电源的工作模式
        # self.dcp_api.dcp_device_set_power_supply_operation_mode(mode="OFF")  # 设置非并联，非串联的电源模式
        # # 打开前面板显示屏
        # self.dcp_api.dcp_device_set_display_state(state="ON")
        # # 启用指定通道输出
        # self.dcp_api.dcp_device_set_specify_channel_output_state(state="ON", chanlist="1")
        # # 选择通道仪表视图
        # self.dcp_api.dcp_device_set_channel_meter_view_mode(mode="METER1")
        # # 同时设置电压和电流
        # self.dcp_api.dcp_device_set_specify_channel_output_voltage_and_current_level(
        #     channel="CH1", voltage=3, current=1)

        # TODO DUT进入接收模式，配置频点（例如2402MHz），接收增益配置最大

        # 频谱分析仪基础参数配置
        # self.sa_api.sa_device_set_restore_defaults(group="ALL")
        self.sa_api.sa_device_set_full_mode_preset()
        time.sleep(1)
        screen_list = self.sa_api.sa_device_query_screen_list()
        if len(screen_list) > 1:
            self.sa_api.sa_device_del_all_screen_except_current_screen()
        self.sa_api.sa_device_select_measurement_application_by_mode_name(mode_name="SA")
        self.sa_api.sa_device_set_specified_measurement(measurement="CHP")

        test_fre = [2402, 2440, 2480]
        p_in = -75
        il = float(self.line_loss)
        for fre in test_fre:
            self.write_info_log("cur_test_fre: {}".format(fre))
            self.vsg_api.vsg_device_set_output_frequency(value=fre, unit="MHz", mode="CW")

            self.sa_api.sa_device_set_center_frequency(freq=fre, unit="MHz")           # 设置center frequency
            self.sa_api.sa_device_ch_power_meas_set_span_value(freq=10, unit="MHz")     # 设置span
            # 设置res bw auto功能关闭，设置res bw value为18kHz
            self.sa_api.sa_device_ch_power_meas_set_res_bw_auto_function(function="OFF")    # 设置res bw auto为off
            self.sa_api.sa_device_ch_power_meas_set_res_bw_freq(freq=18, unit="kHz")        # 设置res bw value为18kHz

            self.vsg_api.vsg_device_set_rf_output_power(value=p_in, unit="DBM")
            self.vsg_api.vsg_device_set_rf_output_modulation_state(state="OFF")
            self.vsg_api.vsg_device_set_rf_output_state(state="ON")

            self.sa_api.sa_device_set_peak_search_mode(mode="MAX")
            self.sa_api.sa_device_ch_power_meas_set_peak_search_by_marker_number(numb=1)        # 设置peak search，using marker <numb>，默认是1
            self.sa_api.sa_device_all_other_measurement_set_trace_type(
                trace_type="AVERage", meas="CHP", trace_numb=1)                                 # swept_sa meas下 Trace Type 选择 Trace Average
            time.sleep(2)

            recv_fre = self.sa_api.sa_device_ch_power_meas_query_marker_frequency_or_time_x_axis_value_by_marker_number(
                numb=1)  # marker1 的功率极值
            self.write_info_log("power_extreme: {}MHz".format(float(recv_fre) / 1E+06))
            recv_power = self.sa_api.sa_device_ch_power_meas_query_marker_amplitude_y_axis_value_by_marker_number(
                numb=1)  # 记录 marker1 的信号强度
            self.write_info_log("signal_strength: {}dBm".format(float(recv_power)))
            p_out = float(recv_power)

            # Pout截屏并保存
            report_sheet_name = "fre_{}".format(fre)
            image_dir_path = os.path.join(self.tc_report_dir_path, report_sheet_name)
            image_name = "{}_Pout_value_captured_screen_image.png".format(report_sheet_name)
            self.write_info_log("截屏保存中...")
            self.sa_api.sa_func_save_screenshot_image(
                image_dir_path=image_dir_path, image_name=image_name)
            self.write_info_log("截屏保存结束，图片名称为：{}".format(image_name))

            # BB_noise截图并保存
            self.vsg_api.vsg_device_set_rf_output_state(state="ON")
            chp = self.sa_api.sa_device_ch_power_meas_query_the_channel_power_dBm()
            self.write_info_log("CHP：{}".format(float(chp)))
            psd = self.sa_api.sa_device_ch_power_meas_query_the_power_spectral_density_dBmHz()
            self.write_info_log("PSD：{}".format(float(psd)))
            bb_noise = float(psd)

            image_name = "{}_bb_noise_value_captured_screen_image.png".format(report_sheet_name)
            self.write_info_log("截屏保存中...")
            self.sa_api.sa_func_save_screenshot_image(
                image_dir_path=image_dir_path, image_name=image_name)
            self.write_info_log("截屏保存结束，图片名称为：{}".format(image_name))

            # 计算噪声系数NF

            nf = 174 - (p_out - p_in - bb_noise) - il
            self.write_info_log("NF: {}".format(nf))

            time.sleep(0.1)

        self.write_info_log(msg="测试结束")
