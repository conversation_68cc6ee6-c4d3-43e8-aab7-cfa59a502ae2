import time
import os
import sys
import numpy as np
from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import <PERSON><PERSON><PERSON><PERSON>
from module_set.draw_class import Mat<PERSON>lotlibPlotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data

        # self.select_dcp = True
        self.select_sa = True
        self.select_vsg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 创建报告文件
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        if ret is False:
            return ret, error
        else:
            self.tc_name = report_path_info["case_name"]
            self.tc_report_path = report_path_info["tc_report_path"]
            self.tc_report_dir_path = report_path_info["tc_report_dir_path"]
            self.tc_set_report_path = report_path_info["tc_set_report_path"]
            self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        # # 配置直流电源
        # self.dcp_api.device_set_reset()
        # # 指定电源的工作模式
        # self.dcp_api.dcp_device_set_power_supply_operation_mode(mode="OFF")  # 设置非并联，非串联的电源模式
        # # 打开前面板显示屏
        # self.dcp_api.dcp_device_set_display_state(state="ON")
        # # 启用指定通道输出
        # self.dcp_api.dcp_device_set_specify_channel_output_state(state="ON", chanlist="1")
        # # 选择通道仪表视图
        # self.dcp_api.dcp_device_set_channel_meter_view_mode(mode="METER1")
        # # 同时设置电压和电流
        # self.dcp_api.dcp_device_set_specify_channel_output_voltage_and_current_level(
        #     channel="CH1", voltage=3, current=1)

        self.vsg_api.vsg_device_set_output_frequency(value=2402, unit="MHz", mode="CW")

        # 频谱分析仪基础参数配置
        # self.sa_api.sa_device_set_restore_defaults(group="ALL")
        self.sa_api.sa_device_set_full_mode_preset()
        time.sleep(1)
        screen_list = self.sa_api.sa_device_query_screen_list()
        if len(screen_list) > 1:
            self.sa_api.sa_device_del_all_screen_except_current_screen()
        self.sa_api.sa_device_select_measurement_application_by_mode_name(mode_name="SA")
        self.sa_api.sa_device_set_specified_measurement(measurement="CHP")

        self.sa_api.sa_device_set_center_frequency(freq=2402, unit="MHz")  # 设置center frequency
        self.sa_api.sa_device_ch_power_meas_set_span_value(freq=10, unit="MHz")  # 设置span
        # 设置res bw auto功能关闭，设置res bw value为18kHz
        self.sa_api.sa_device_ch_power_meas_set_res_bw_auto_function(function="OFF")  # 设置res bw auto为off
        self.sa_api.sa_device_ch_power_meas_set_res_bw_freq(freq=18, unit="kHz")  # 设置res bw value为18kHz

        # TODO DUT进入接收模式，配置频点（例如2402MHz），接收增益档位配置0

        # gain_step_limit_range = {
        #     "0x00": [-60, 0, 5],
        #     "0x01": [-60, 0, 5],
        # }

        start_gain = 0x00
        end_gain = 0x1f

        gain_step_limit_range = dict()
        for gain in range(start_gain, end_gain + 1, 1):
            rx_gain = "{:#04x}".format(gain)  # '0x00'
            gain_step_limit_range[rx_gain] = [-60, 0, 5]

        for gain in range(start_gain, end_gain + 1, 1):
            rx_gain = "{:#04x}".format(gain)        # '0x00'
            bin_gain = "{:#07b}".format(gain)       # '0b00000'
            lna_gain = bin_gain[2:][:2]
            fltr_gain = bin_gain[2:][2:3]
            mxr_gain = bin_gain[2:][3:]

            limit_range = gain_step_limit_range[rx_gain]
            start_power = limit_range[0]
            end_power = limit_range[1]
            power_step = limit_range[2]

            if gain == start_gain:
                report_title = [
                    "RX Gain（Register）", "LNA Gain（Register）", "Fltr Gain（Register）",
                    "Mxr Gain（Register）", "输入功率（dBm）", "输出强度（dBm）", "增益（dB）"]
                self.handle_report.write_report_title_data(sheet_name=self.tc_name, data=report_title)
            for p_in in range(start_power, end_power + 1, power_step):
                self.write_info_log("cur_rx_gain: {}, cur_Pin: {}".format(rx_gain, p_in))
                self.vsg_api.vsg_device_set_rf_output_power(value=p_in, unit="DBM")
                self.vsg_api.vsg_device_set_rf_output_modulation_state(state="OFF")
                self.vsg_api.vsg_device_set_rf_output_state(state="ON")
                time.sleep(0.1)
                self.sa_api.sa_device_set_peak_search_mode(mode="MAX")
                self.sa_api.sa_device_ch_power_meas_set_peak_search_by_marker_number(
                    numb=1)  # 设置peak search，using marker <numb>，默认是1
                self.sa_api.sa_device_all_other_measurement_set_trace_type(
                    trace_type="AVERage", meas="CHP", trace_numb=1)  # swept_sa meas下 Trace Type 选择 Trace Average
                time.sleep(2)

                recv_fre = self.sa_api.sa_device_ch_power_meas_query_marker_frequency_or_time_x_axis_value_by_marker_number(
                    numb=1)  # marker1 的功率极值
                self.write_info_log("power_extreme: {}MHz".format(float(recv_fre) / 1E+06))
                recv_power = self.sa_api.sa_device_ch_power_meas_query_marker_amplitude_y_axis_value_by_marker_number(
                    numb=1)  # 记录 marker1 的信号强度
                self.write_info_log("signal_strength: {}dBm".format(float(recv_power)))
                p_out = float(recv_power)

                single_test_result = [rx_gain, lna_gain, fltr_gain, mxr_gain, p_in, p_out, '']
                self.handle_report.write_report_test_data(sheet_name=self.tc_name, data=single_test_result)

                self.sa_api.sa_device_all_other_measurement_set_trace_type(
                    trace_type="WRITe", meas="CHP", trace_numb=1)  # swept_sa meas下 Trace Type 选择 WRITe
                time.sleep(0.1)

        self.write_info_log(msg="测试结束")
