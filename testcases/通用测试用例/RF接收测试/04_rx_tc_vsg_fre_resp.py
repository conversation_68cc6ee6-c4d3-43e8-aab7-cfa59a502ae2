import time
import os
import sys
import numpy as np
from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import <PERSON><PERSON><PERSON><PERSON>
from module_set.draw_class import Mat<PERSON>lotlibPlotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data

        # self.select_dcp = True
        self.select_sa = True
        self.select_vsg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 创建报告文件
        ret, error, report_path_info = self.init_cur_testcase_report_file(cur_tc_file=__file__)
        if ret is False:
            return ret, error
        else:
            self.tc_name = report_path_info["case_name"]
            self.tc_report_path = report_path_info["tc_report_path"]
            self.tc_report_dir_path = report_path_info["tc_report_dir_path"]
            self.tc_set_report_path = report_path_info["tc_set_report_path"]
            self.handle_report = HandleExcelReport(filename=self.tc_report_path)
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")

        # # 配置直流电源
        # self.dcp_api.device_set_reset()
        # # 指定电源的工作模式
        # self.dcp_api.dcp_device_set_power_supply_operation_mode(mode="OFF")  # 设置非并联，非串联的电源模式
        # # 打开前面板显示屏
        # self.dcp_api.dcp_device_set_display_state(state="ON")
        # # 启用指定通道输出
        # self.dcp_api.dcp_device_set_specify_channel_output_state(state="ON", chanlist="1")
        # # 选择通道仪表视图
        # self.dcp_api.dcp_device_set_channel_meter_view_mode(mode="METER1")
        # # 同时设置电压和电流
        # self.dcp_api.dcp_device_set_specify_channel_output_voltage_and_current_level(
        #     channel="CH1", voltage=3, current=1)

        # TODO DUT进入接收模式，配置频点（例如2402MHz），接收增益配置最大

        # 频谱分析仪基础参数配置
        # self.sa_api.sa_device_set_restore_defaults(group="ALL")
        self.sa_api.sa_device_set_full_mode_preset()
        time.sleep(1)
        screen_list = self.sa_api.sa_device_query_screen_list()
        if len(screen_list) > 1:
            self.sa_api.sa_device_del_all_screen_except_current_screen()
        self.sa_api.sa_device_select_measurement_application_by_mode_name(mode_name="SA")
        self.sa_api.sa_device_set_specified_measurement(measurement="CHP")

        self.sa_api.sa_device_set_center_frequency(freq=2402, unit="MHz")           # 设置center frequency
        self.sa_api.sa_device_ch_power_meas_set_span_value(freq=10, unit="MHz")     # 设置span
        # 设置res bw auto功能打开，res bw默认为91kHz
        self.sa_api.sa_device_ch_power_meas_set_res_bw_auto_function(function="ON")     # 设置res bw为auto
        # 或者设置res bw auto功能关闭，设置res bw value为91kHz
        # self.sa_api.sa_device_ch_power_meas_set_res_bw_auto_function(function="OFF")    # 设置res bw auto为off
        # self.sa_api.sa_device_ch_power_meas_set_res_bw_freq(freq=91, unit="kHz")        # 设置res bw value为91kHz

        n = 1  # 频偏系数
        max_n = 40
        step = 0.1
        recv_filter_fre_resp_result = list()
        for i in range(n, max_n + 1, 1):
            test_fre = 2402 + (i * step)
            self.write_info_log("test_fre: {}".format(test_fre))
            self.vsg_api.vsg_device_set_output_frequency(value=test_fre, unit="MHz", mode="CW")
            self.vsg_api.vsg_device_set_rf_output_power(value=-35, unit="DBM")
            self.vsg_api.vsg_device_set_rf_output_modulation_state(state="OFF")
            self.vsg_api.vsg_device_set_rf_output_state(state="ON")

            self.sa_api.sa_device_set_peak_search_mode(mode="MAX")
            self.sa_api.sa_device_ch_power_meas_set_peak_search_by_marker_number(numb=1)          # 设置peak search，using marker <numb>，默认是1
            self.sa_api.sa_device_all_other_measurement_set_trace_type(
                trace_type="AVERage", meas="CHP", trace_numb=1)                                   # swept_sa meas下 Trace Type 选择 Trace Average
            time.sleep(2)

            recv_fre = self.sa_api.sa_device_ch_power_meas_query_marker_frequency_or_time_x_axis_value_by_marker_number(
                numb=1)  # marker1 的功率极值
            self.write_info_log("power_extreme: {}MHz".format(float(recv_fre) / 1E+06))
            recv_power = self.sa_api.sa_device_ch_power_meas_query_marker_amplitude_y_axis_value_by_marker_number(
                numb=1)  # 记录 marker1 的信号强度
            self.write_info_log("signal_strength: {}dBm".format(float(recv_power)))

            if range(n, max_n + 1, 1).index(i) == 0:
                report_title = ["Frequency(MHz)", "Power(dBm)"]
                self.handle_report.write_report_title_data(sheet_name=self.tc_name, data=report_title)
            single_test_result = (float(recv_fre) / 1E+06, float(recv_power))
            recv_filter_fre_resp_result.append(single_test_result)
            self.handle_report.write_report_test_data(sheet_name=self.tc_name, data=single_test_result)
            # self.sa_api.sa_device_all_other_measurement_set_trace_type(
            #     trace_type="WRITe", meas="CHP", trace_numb=1)  # swept_sa meas下 Trace Type 选择 Trace Average
            time.sleep(0.1)
            # 测试获取channel power和psd数据
            # ret = self.sa_api.sa_device_ch_power_meas_query_retrieves_data_defined_by_n(n=1)
            # self.write_info_log(ret)
            # ret = self.sa_api.sa_device_ch_power_meas_query_the_channel_power_dBm()
            # self.write_info_log("{}".format(float(ret)))
            # ret = self.sa_api.sa_device_ch_power_meas_query_the_power_spectral_density_dBmHz()
            # self.write_info_log("{}".format(float(ret)))
        # 画图
        x_axis_data = [item[0] for item in recv_filter_fre_resp_result]
        y_axis_data = [item[1] for item in recv_filter_fre_resp_result]
        mp = MatplotlibPlotter(x_axis_data, y_axis_data)
        pic_buf = mp.draw_single_line_graph(title=self.tc_name, x_label="Frequency(MHz)", y_label="Power(dBm)")
        self.handle_report.write_report_image_data(sheet_name=self.tc_name, image_data=pic_buf)

        self.write_info_log(msg="测试结束")
