import time
import os
import sys
import numpy as np

from module_set.device_run_func_class import DeviceRun
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
# from module_set.parse_config_data import ParseConfigData
from module_set.draw_class import Mat<PERSON>lotlibPlotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.select_vsg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        try:
            ret, data = self.edit_cur_testcase_test_process()
            if ret is False:
                self.disconnection_device()
                return ret, data
        except Exception as e:
            error = str(e)
            self.disconnection_device()
            return False, error

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")
        print("line_loss: {}".format(self.line_loss))
        
        ret, data = self.uart_handle.check_for_correct_reg_addr(reg_addr='0x01000001', rw_flag='r')
        if ret is False:
            # self.write_error_log(data)
            return ret, data
        else:
            self.write_info_log("reg_addr: {}".format(data))
            
        ret, data = self.uart_handle.check_for_correct_reg_val(reg_val='0xff')
        if ret is False:
            # self.write_error_log(data)
            return ret, data
        else:
            self.write_info_log("reg_val: {}".format(data))
        time.sleep(10)
        self.write_info_log(msg="测试结束")
        return True, None


if __name__ == "__main__":
    from module_set.public_data import test_data
    # 获取当前文件的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_path = os.path.dirname(current_dir)

    event_engine = EventEngine()
    case_run = CaseRun(data=test_data, event_engine=event_engine)
    case_run.run()
