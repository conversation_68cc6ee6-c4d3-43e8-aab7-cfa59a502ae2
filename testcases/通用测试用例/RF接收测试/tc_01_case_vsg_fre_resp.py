import time
import os
import sys

from gxapp.logengine import LogData, EVENT_LOG, EVENT_LOG_SET_DIR, LogDirData
from gxapp.bttengine_run_data import EventEngine, Event, BttRunData
from module_set.device_run_func_class import Devi<PERSON><PERSON><PERSON>
from module_set.draw_class import <PERSON><PERSON><PERSON><PERSON>b<PERSON>lotter
from module_set.handle_report_class import HandleExcelReport


class CaseRun(DeviceRun):

    def __init__(self, data: BttRunData, event_engine: EventEngine):
        super().__init__(data=data, event_engine=event_engine)
        self.event_engine = event_engine
        self.data = data
        self.select_dcp = True
        self.select_sa = True
        self.select_vsg = True

    def run(self):
        # 初始化设备和校验设备连接
        ret, error = self.init_device_data_and_check_connection_correct()
        if ret is False:
            return ret, error
        # 编辑当前用例流程
        self.edit_cur_testcase_test_process()

        # 测试结束，断开所有连接
        self.disconnection_device()
        return True, error

    def edit_cur_testcase_test_process(self):
        self.write_info_log(msg="开始测试")
        self.dcp_api.device_set_reset()

        # 配置直流电源
        # 指定电源的工作模式
        self.dcp_api.dcp_device_set_power_supply_operation_mode(mode="OFF")  # 设置非并联，非串联的电源模式
        # 打开前面板显示屏
        self.dcp_api.dcp_device_set_display_state(state="ON")
        # 启用指定通道输出
        self.dcp_api.dcp_device_set_specify_channel_output_state(state="ON", chanlist="1")
        # 选择通道仪表视图
        self.dcp_api.dcp_device_set_channel_meter_view_mode(mode="METER1")
        # 同时设置电压和电流
        self.dcp_api.dcp_device_set_specify_channel_output_voltage_and_current_level(
            channel="CH1", voltage=3, current=1)

        # TODO DUT进入接收模式，配置频点（例如2402MHz），接收增益配置最大

        # 频谱分析仪基础参数配置
        self.sa_api.sa_device_set_restore_defaults(group="ALL")
        # self.sa_api.sa_device_set_full_mode_preset()
        time.sleep(1)
        self.sa_api.sa_device_select_measurement_application_by_mode_name(mode_name="SA")
        self.sa_api.sa_device_set_specified_measurement(measurement="CHP")

        self.sa_api.sa_device_set_center_frequency(freq=5, unit="MHz")              # 设置center frequency
        self.sa_api.sa_device_set_span_value(freq=10, unit="MHz")                   # 设置span
        self.sa_api.sa_device_swept_sa_measurement_set_trace_type(
            trace_type="AVERage", trace_numb=1)                                     # swept_sa meas下 Trace Type 选择 Trace Average
        # 置res bw auto功能打开，res bw默认为91kHz
        self.sa_api.sa_device_set_res_bw_auto_function(function="ON")               # 设置res bw为auto
        # 或者设置res bw auto功能关闭，设置res bw value为91kHz
        self.sa_api.sa_device_set_res_bw_auto_function(function="OFF")              # 设置res bw auto为off
        self.sa_api.sa_device_set_res_bw_freq(freq=91, unit="kHz")                  # 设置res bw value为91kHz
        self.sa_api.sa_device_set_peak_search_mode(mode="MAX")                      # 设置peak search的模式为MAX

        n = 1  # 频偏系数
        max_n = 40
        step = 0.1
        for n in range(n, max_n + 1, 1):
            test_fre = 2402 + n * step
            self.vsg_api.vsg_device_set_output_frequency(value=2402, unit="MHz", mode="CW")
            self.vsg_api.vsg_device_set_rf_output_power(value=-75, unit="DBM")
            self.vsg_api.vsg_device_set_rf_output_modulation_state(state="OFF")
            self.vsg_api.self.vsg_api.vsg_device_set_rf_output_state(state="ON")

            self.sa_api.sa_device_set_peak_search_by_marker_number(numb=1)          # 设置peak search，using marker <numb>，默认是1
            self.sa_api.sa_device_query_marker_amplitude_y_axis_value_by_marker_number(numb=1)  # 记录 marker1 的信号强度

        self.write_info_log(msg="测试结束")
